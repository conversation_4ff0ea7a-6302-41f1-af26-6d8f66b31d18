from application import create_app, db
from application.models import User
from werkzeug.security import generate_password_hash
from datetime import datetime, timezone

app = create_app()

# إعادة تعيين كلمة المرور للمستخدم admin
with app.app_context():
    # البحث عن المستخدم admin
    admin_user = User.query.filter_by(username="admin").first()
    
    if admin_user:
        # تعيين كلمة المرور الجديدة
        new_password = "admin123"
        admin_user.password = generate_password_hash(new_password)
        
        # حفظ التغييرات
        db.session.commit()
        
        print("=" * 50)
        print("تم إعادة تعيين كلمة المرور بنجاح!")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور الجديدة: {new_password}")
        print("=" * 50)
    else:
        print("لم يتم العثور على المستخدم admin!")
        
        # إنشاء مستخدم admin جديد
        print("جاري إنشاء مستخدم admin جديد...")
        new_admin = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),
            role="admin",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(new_admin)
        db.session.commit()
        
        print("=" * 50)
        print("تم إنشاء مستخدم admin جديد بنجاح!")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور: admin123")
        print("=" * 50)
