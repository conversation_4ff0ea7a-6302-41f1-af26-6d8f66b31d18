<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الموظف: {{ employee.name }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.rtl.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <style>
        @page {
            size: A4;
            margin: 1.5cm;
        }
        body {
            font-family: 'Arial', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            background-color: white;
            color: black;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
        .no-print {
            display: none !important;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #0056b3;
        }
        .header h2 {
            font-size: 16pt;
            font-weight: bold;
            margin: 3px 0;
            color: #0056b3;
        }
        .header h3 {
            font-size: 14pt;
            font-weight: bold;
            margin: 3px 0;
            color: #0056b3;
        }
        .header h4 {
            font-size: 12pt;
            font-weight: bold;
            margin: 3px 0;
            color: #0056b3;
        }
        .employee-photo {
            text-align: center;
            margin-bottom: 10px;
            float: right;
        }
        .employee-photo img {
            width: 120px;
            height: 150px;
            border: 1px solid #0056b3;
            padding: 2px;
            background: white;
            object-fit: cover;
        }
        .qr-code {
            text-align: center;
            margin-bottom: 10px;
            float: left;
        }
        .qr-code img {
            width: 75px; /* 2cm */
            height: 75px; /* 2cm */
            border: 1px solid #0056b3;
            padding: 2px;
            background: white;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .data-table th, .data-table td {
            border: 1px solid #0056b3;
            padding: 5px 8px;
            text-align: right;
        }
        .data-table th {
            background-color: #e3f2fd;
            font-weight: bold;
            color: #0056b3;
        }
        .section-title {
            background-color: #e3f2fd;
            padding: 5px 10px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #0056b3;
            margin-bottom: 10px;
            color: #0056b3;
        }
        .signature {
            margin-top: 20px;
            text-align: center;
        }
        .signature p {
            margin-bottom: 15px;
            font-weight: bold;
            color: #0056b3;
        }
        .signature-line {
            display: inline-block;
            width: 150px;
            border-bottom: 1px solid #0056b3;
            margin-bottom: 3px;
        }
        .print-container {
            max-width: 21cm;
            margin: 0 auto;
            background-color: white;
            padding: 0.5cm;
            min-height: 29.7cm;
            position: relative;
        }
        .col-md-4 {
            width: 33.33%;
            float: right;
        }
        .col-md-6 {
            width: 50%;
            float: right;
        }
        .col-md-3 {
            width: 25%;
            float: right;
        }
        .col-md-9 {
            width: 75%;
            float: right;
        }
        .col-md-12 {
            width: 100%;
            float: right;
        }
        .row {
            display: block;
            width: 100%;
            clear: both;
        }
        .row:after {
            clear: both;
            content: "";
            display: table;
        }
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
            border: 1px solid #000;
        }
        .bg-success { background-color: #e8f5e9; color: #000; }
        .bg-danger { background-color: #ffebee; color: #000; }
        .bg-info { background-color: #e3f2fd; color: #000; }
        .bg-warning { background-color: #fff8e1; color: #000; }
        .bg-secondary { background-color: #eceff1; color: #000; }
        .bg-primary { background-color: #e3f2fd; color: #000; }
        .bg-dark { background-color: #eceff1; color: #000; }
        @media print {
            body {
                background-color: white;
                font-size: 10pt;
            }
            .print-container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            .badge {
                border: 1px solid #0056b3 !important;
            }
            .bg-success {
                background-color: #e8f5e9 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-danger {
                background-color: #ffebee !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-info {
                background-color: #e3f2fd !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-warning {
                background-color: #fff8e1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-secondary {
                background-color: #eceff1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-primary {
                background-color: #e3f2fd !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-dark {
                background-color: #eceff1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .no-print {
                display: none !important;
            }
            .qr-code {
                display: block !important;
                visibility: visible !important;
            }
            .qr-code img {
                display: block !important;
                visibility: visible !important;
            }
            .data-table th {
                background-color: #e3f2fd !important;
                color: #0056b3 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .section-title {
                background-color: #e3f2fd !important;
                color: #0056b3 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .data-table th, .data-table td {
                border: 1px solid #0056b3 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="print-container">
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h2><i class="fas fa-print"></i> طباعة بيانات الموظف</h2>
            <div>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة إلى بيانات الموظف
                </a>
            </div>
        </div>

        <div class="header">
            <h2>وزارة الداخلية</h2>
            <h3>مديرية أمن الخمس</h3>
            <h4>قسم المرور والتراخيص الخمس</h4>
        </div>

        <div class="row" style="margin-bottom: 20px;">
            <!-- صورة الموظف على اليمين -->
            <div class="employee-photo" style="width: 25%; float: right;">
                {% if employee.profile_image %}
                <img src="{{ url_for('static', filename='img/employees/' + employee.profile_image) }}" alt="صورة الموظف">
                {% else %}
                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="صورة الموظف">
                {% endif %}
            </div>

            <!-- كود QR على اليسار -->
            <div class="qr-code" style="width: 25%; float: left;">
                <img src="{{ qr_code }}" alt="QR Code">
            </div>

            <!-- عنوان في الوسط -->
            <div style="width: 50%; float: right; text-align: center;">
                <h3 style="margin-top: 30px; color: #0056b3;">الســـيد : {{ employee.name }}</h3>

            </div>
        </div>

        <div style="clear: both;"></div>

        <div class="section-title">البيانات الشخصية</div>

        <table class="data-table">
            <tr>
                <th width="20%">الاسم</th>
                <td width="30%">{{ employee.name }}</td>
                <th width="20%">الرتبة</th>
                <td width="30%">{{ employee.military_rank or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>الرقم العسكري</th>
                <td>{{ employee.military_id }}</td>
                <th>الرقم الوطني</th>
                <td>{{ employee.national_id or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>تاريخ الميلاد</th>
                <td>{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير محدد' }}</td>
                <th>مكان الميلاد</th>
                <td>{{ employee.birth_place or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>فصيلة الدم</th>
                <td>{{ employee.blood_type or 'غير محدد' }}</td>
                <th>السكن الحالي</th>
                <td>{{ employee.current_residence or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">البيانات التعليمية والمهنية</div>

        <table class="data-table">
            <tr>
                <th width="20%">المؤهل العلمي</th>
                <td width="30%">{{ employee.education or 'غير محدد' }}</td>
                <th width="20%">تاريخ المؤهل</th>
                <td width="30%">{{ employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>الفئة</th>
                <td>{{ employee.category.value if employee.category else 'غير محدد' }}</td>
                <th>الوحدة</th>
                <td>{{ employee.unit }}</td>
            </tr>
            <tr>
                <th>العمل المكلف به</th>
                <td>{{ employee.position }}</td>
                <th>الحالة</th>
                <td>
                    <span class="badge
                        {% if employee.status.name == 'ACTIVE' %}bg-success
                        {% elif employee.status.name == 'ABSENT' %}bg-danger
                        {% elif employee.status.name == 'ASSIGNED' %}bg-info
                        {% elif employee.status.name == 'ON_LEAVE' %}bg-warning
                        {% elif employee.status.name == 'SUSPENDED' %}bg-secondary
                        {% elif employee.status.name == 'SCATTERED' %}bg-primary
                        {% elif employee.status.name == 'MEDICAL' %}bg-dark
                        {% endif %}">
                        {{ employee.status.value }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>تاريخ التعيين</th>
                <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد' }}</td>
                <th>تاريخ آخر ترقية</th>
                <td>{{ employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">بيانات الاتصال والبيانات المالية</div>

        <table class="data-table">
            <tr>
                <th width="20%">رقم الهاتف</th>
                <td width="30%">{{ employee.phone or 'غير محدد' }}</td>
                <th width="20%">البريد الإلكتروني</th>
                <td width="30%">{{ employee.email or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>المصرف</th>
                <td>{{ employee.bank_name or 'غير محدد' }}</td>
                <th>رقم الحساب المصرفي</th>
                <td>{{ employee.bank_account or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">بيانات الإجازات</div>

        <table class="data-table">
            <tr>
                <th width="20%">رصيد الإجازة</th>
                <td width="30%">{{ employee.leave_balance }} يوم</td>
                <th width="20%">ملاحظات الحالة</th>
                <td width="30%">{{ employee.status_notes or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="qr-code" style="position: absolute; bottom: 20px; left: 20px;">

        </div>

        <div class="signature">
            <p>يعتمد</p>
            <div class="signature-line"></div>
            <div class="text-center" style="font-size: 9pt; margin-top: 5px;">
                <p>المستخدم: {{ current_user.username }} | تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>

        <!-- Footer removed as requested -->
        </div> <!-- end of print-container -->
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
