{% extends 'layouts/base.html' %}

{% block title %}لوحة التحكم - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="text-center mb-4">مرحباً بك في نظام إدارة الموارد البشرية</h2>
                <p class="text-center">نظام متكامل لإدارة الموظفين والإجازات في قسم المرور والتراخيص الخمس</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card shadow border-primary mb-3">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">
                    <i class="fas fa-users"></i>
                </div>
                <h5 class="card-title">إجمالي الموظفين</h5>
                <p class="card-text display-6">{{ stats.employee_count }}</p>
                <a href="{{ url_for('employees.index') }}" class="btn btn-primary">إدارة الموظفين</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card shadow border-success mb-3">
            <div class="card-body text-center">
                <div class="display-4 text-success mb-3">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h5 class="card-title">الإجازات المعتمدة</h5>
                <p class="card-text display-6">{{ stats.approved_leaves }}</p>
                <a href="{{ url_for('leaves.index') }}?status=2" class="btn btn-success">عرض الإجازات المعتمدة</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card shadow border-warning mb-3">
            <div class="card-body text-center">
                <div class="display-4 text-warning mb-3">
                    <i class="fas fa-clock"></i>
                </div>
                <h5 class="card-title">طلبات الإجازة المعلقة</h5>
                <p class="card-text display-6">{{ stats.pending_leaves }}</p>
                <a href="{{ url_for('leaves.index') }}?status=1" class="btn btn-warning">عرض الطلبات المعلقة</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card shadow border-info mb-3">
            <div class="card-body text-center">
                <div class="display-4 text-info mb-3">
                    <i class="fas fa-building"></i>
                </div>
                <h5 class="card-title">الأقسام</h5>
                <p class="card-text display-6">{{ stats.department_count }}</p>
                <a href="#" class="btn btn-info">عرض الأقسام</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الموظفين حسب الرتب
                </h5>
            </div>
            <div class="card-body">
                <canvas id="rankChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>الإجازات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="leaveChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="deptChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-alt me-2"></i>أحدث طلبات الإجازة
                </h5>
                <a href="{{ url_for('leaves.index') }}" class="btn btn-sm btn-light">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave, employee in recent_leaves %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>{{ leave.leave_type_rel.name }}</td>
                                <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif leave.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('leaves.view', id=leave.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد طلبات إجازة حديثة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Rank distribution chart
        const rankCtx = document.getElementById('rankChart').getContext('2d');
        const rankChart = new Chart(rankCtx, {
            type: 'pie',
            data: {
                labels: {{ rank_names|tojson }},
                datasets: [{
                    label: 'عدد الموظفين',
                    data: {{ rank_values|tojson }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'توزيع الموظفين حسب الرتب'
                    }
                }
            }
        });

        // Department distribution chart
        const deptCtx = document.getElementById('deptChart').getContext('2d');
        const deptChart = new Chart(deptCtx, {
            type: 'pie',
            data: {
                labels: {{ dept_names|tojson }},
                datasets: [{
                    label: 'عدد الموظفين',
                    data: {{ dept_values|tojson }},
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'توزيع الموظفين حسب الأقسام'
                    }
                }
            }
        });

        // Monthly leaves chart
        const leaveCtx = document.getElementById('leaveChart').getContext('2d');
        const leaveChart = new Chart(leaveCtx, {
            type: 'bar',
            data: {
                labels: {{ month_names|tojson }},
                datasets: [{
                    label: 'الإجازات المعتمدة',
                    data: {{ approved_data|tojson }},
                    backgroundColor: 'rgba(75, 192, 192, 0.7)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }, {
                    label: 'الإجازات المرفوضة',
                    data: {{ rejected_data|tojson }},
                    backgroundColor: 'rgba(255, 99, 132, 0.7)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }, {
                    label: 'الإجازات المعلقة',
                    data: {{ pending_data|tojson }},
                    backgroundColor: 'rgba(255, 206, 86, 0.7)',
                    borderColor: 'rgba(255, 206, 86, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'الإجازات الشهرية'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
