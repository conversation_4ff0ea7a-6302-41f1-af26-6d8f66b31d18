// Auth JavaScript file for HRM System

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);
    
    // Add animation to form elements
    const formElements = document.querySelectorAll('.form-control, .btn');
    formElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out';
        element.style.transitionDelay = `${index * 0.1}s`;
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 100);
    });
    
    // Focus on first input field
    const firstInput = document.querySelector('.form-control');
    if (firstInput) {
        firstInput.focus();
    }
});
