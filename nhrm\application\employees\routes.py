from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from .. import db
from ..models import Employee, Department, User, AuditLog, LeaveRequest
from .forms import EmployeeForm, EmployeeSearchForm, ImportForm
import os
import uuid
import pandas as pd
from io import BytesIO
import json
from datetime import datetime, timezone, timedelta

employees_bp = Blueprint('employees', __name__)

@employees_bp.route('/')
@login_required
def index():
    # Create search form
    form = EmployeeSearchForm()

    # لم نعد بحاجة إلى تعبئة قائمة الوحدات لأنها أصبحت إدخال حر

    # Get all unique ranks for the rank dropdown
    ranks = db.session.query(Employee.military_rank).distinct().all()
    form.rank.choices = [(0, 'جميع الرتب')] + [
        (i+1, rank[0]) for i, rank in enumerate(ranks) if rank[0]
    ]

    # Process search parameters
    search_query = request.args.get('search', '')
    department_query = request.args.get('department', '')
    rank_id = request.args.get('rank', type=int)

    # Advanced search parameters
    advanced_search = request.args.get('advanced_search', 'false') == 'true'
    name_query = request.args.get('name', '')
    military_id_query = request.args.get('military_id', '')
    rank_query = request.args.get('rank_query', '')
    unit_query = request.args.get('unit', '')
    category_query = request.args.get('category', '')
    status_query = request.args.get('status', '')
    position_query = request.args.get('position', '')
    national_id_query = request.args.get('national_id', '')

    # Base query
    query = Employee.query

    # Apply basic filters if not using advanced search
    if not advanced_search:
        if search_query:
            query = query.filter(Employee.name.ilike(f'%{search_query}%') |
                                Employee.military_id.ilike(f'%{search_query}%'))

        if department_query:
            query = query.filter(Employee.department_name.ilike(f'%{department_query}%'))

        if rank_id and rank_id > 0:
            selected_rank = form.rank.choices[rank_id][1]
            query = query.filter(Employee.military_rank == selected_rank)
    # Apply advanced filters
    else:
        if name_query:
            query = query.filter(Employee.name.ilike(f'%{name_query}%'))

        if military_id_query:
            query = query.filter(Employee.military_id.ilike(f'%{military_id_query}%'))

        if rank_query:
            query = query.filter(Employee.military_rank.ilike(f'%{rank_query}%'))

        if unit_query:
            query = query.filter(Employee.department_name.ilike(f'%{unit_query}%'))

        if category_query:
            query = query.filter(Employee.employee_category == category_query)

        if status_query:
            query = query.filter(Employee.employee_status == status_query)

        if position_query:
            query = query.filter(Employee.position.ilike(f'%{position_query}%'))

        if national_id_query:
            query = query.filter(Employee.national_id.ilike(f'%{national_id_query}%'))

    # Get paginated results
    page = request.args.get('page', 1, type=int)
    employees = query.order_by(Employee.name).paginate(page=page, per_page=10)

    return render_template('employees/index.html',
                          employees=employees,
                          form=form,
                          search_query=search_query,
                          department_query=department_query,
                          rank_id=rank_id,
                          advanced_search=advanced_search,
                          name_query=name_query,
                          military_id_query=military_id_query,
                          rank_query=rank_query,
                          unit_query=unit_query,
                          category_query=category_query,
                          status_query=status_query,
                          position_query=position_query,
                          national_id_query=national_id_query)

@employees_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = EmployeeForm()

    if form.validate_on_submit():
        # Handle profile image upload
        profile_image = None
        if form.profile_image.data and hasattr(form.profile_image.data, 'filename'):
            filename = secure_filename(form.profile_image.data.filename)
            # Create unique filename
            unique_filename = f"{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}_{filename}"
            # Ensure directory exists
            upload_dir = os.path.join(current_app.root_path, 'static/img/profiles')
            os.makedirs(upload_dir, exist_ok=True)
            # Save file
            form.profile_image.data.save(os.path.join(upload_dir, unique_filename))
            profile_image = f"profiles/{unique_filename}"

        # Create new employee
        employee = Employee(
            name=form.name.data,
            position=form.position.data,
            department_name=form.department_name.data,
            military_rank=form.military_rank.data,
            military_id=form.military_id.data,
            national_id=form.national_id.data,
            blood_type=form.blood_type.data,
            employee_category=form.employee_category.data,
            birth_place=form.birth_place.data,
            education=form.education.data,
            education_date=form.education_date.data,
            bank_name=form.bank_name.data,
            bank_account=form.bank_account.data,
            phone=form.phone.data,
            address=form.address.data,
            date_of_birth=form.date_of_birth.data,
            hire_date=form.hire_date.data,
            last_promotion_date=form.last_promotion_date.data,
            employee_status=form.employee_status.data,
            status_notes=form.status_notes.data,
            profile_image=profile_image
        )

        db.session.add(employee)

        # Log the action
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='employee',
            entity_id=employee.id,
            details=f"تم إنشاء موظف جديد: {employee.name}",
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة الموظف بنجاح', 'success')
        return redirect(url_for('employees.index'))

    return render_template('employees/create.html', form=form)

@employees_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    if form.validate_on_submit():
        # Handle profile image upload
        if form.profile_image.data and hasattr(form.profile_image.data, 'filename'):
            # Delete old profile image if exists
            if employee.profile_image:
                old_image_path = os.path.join(current_app.root_path, 'static/img', employee.profile_image)
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)

            filename = secure_filename(form.profile_image.data.filename)
            # Create unique filename
            unique_filename = f"{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}_{filename}"
            # Ensure directory exists
            upload_dir = os.path.join(current_app.root_path, 'static/img/profiles')
            os.makedirs(upload_dir, exist_ok=True)
            # Save file
            form.profile_image.data.save(os.path.join(upload_dir, unique_filename))
            employee.profile_image = f"profiles/{unique_filename}"

        # Update employee data
        employee.name = form.name.data
        employee.position = form.position.data
        employee.department_name = form.department_name.data
        employee.military_rank = form.military_rank.data
        employee.military_id = form.military_id.data
        employee.national_id = form.national_id.data
        employee.blood_type = form.blood_type.data
        employee.employee_category = form.employee_category.data
        employee.birth_place = form.birth_place.data
        employee.education = form.education.data
        employee.education_date = form.education_date.data
        employee.bank_name = form.bank_name.data
        employee.bank_account = form.bank_account.data
        employee.phone = form.phone.data
        employee.address = form.address.data
        employee.date_of_birth = form.date_of_birth.data
        employee.hire_date = form.hire_date.data
        employee.last_promotion_date = form.last_promotion_date.data
        employee.employee_status = form.employee_status.data
        employee.status_notes = form.status_notes.data

        # Log the action
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='employee',
            entity_id=employee.id,
            details=f"تم تحديث بيانات الموظف: {employee.name}",
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('employees.index'))

    return render_template('employees/edit.html', form=form, employee=employee)

@employees_bp.route('/<int:id>/view')
@login_required
def view(id):
    employee = Employee.query.get_or_404(id)

    # Generate QR code for employee
    from ..utilities import generate_employee_qr
    qr_code = generate_employee_qr(employee)

    return render_template('employees/view.html', employee=employee, qr_code=qr_code)

@employees_bp.route('/qr/<int:id>')
def qr_view(id):
    """View employee data from QR code scan"""
    employee = Employee.query.get_or_404(id)
    return render_template('employees/qr_view.html', employee=employee)

@employees_bp.route('/<int:id>/print')
@login_required
def print_employee(id):
    employee = Employee.query.get_or_404(id)

    # Calculate leave balance
    # Assuming each employee gets 30 days of annual leave
    annual_leave_balance = 30

    # Calculate used leave days in the current year
    current_year = datetime.now().year
    start_of_year = datetime(current_year, 1, 1)
    end_of_year = datetime(current_year, 12, 31)

    used_leave = db.session.query(db.func.sum(LeaveRequest.total_days)).filter(
        LeaveRequest.employee_id == id,
        LeaveRequest.status == 'approved',
        LeaveRequest.start_date >= start_of_year,
        LeaveRequest.end_date <= end_of_year
    ).scalar() or 0

    # Calculate remaining balance
    leave_balance = annual_leave_balance - used_leave

    # Generate QR code for employee
    from ..utilities import generate_employee_qr
    qr_code = generate_employee_qr(employee)

    return render_template('employees/print.html',
                          employee=employee,
                          leave_balance=leave_balance,
                          used_leave=used_leave,
                          now=datetime.now,
                          qr_code=qr_code)

@employees_bp.route('/import', methods=['GET', 'POST'])
@login_required
def import_employees():
    form = ImportForm()

    if form.validate_on_submit():
        try:
            # Save the uploaded file
            file = form.file.data
            update_existing = form.update_existing.data

            # Read Excel file with openpyxl engine
            try:
                df = pd.read_excel(file, engine='openpyxl')
                print(f"Successfully read Excel file with {len(df)} rows")
            except Exception as e:
                flash(f'خطأ في قراءة الملف: {str(e)}', 'danger')
                return redirect(url_for('employees.import_employees'))

            # Check required columns
            required_columns = ['الاسم', 'الرقم العسكري', 'الوحدة', 'العمل المكلف به']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                flash(f'الملف يفتقد الأعمدة التالية: {", ".join(missing_columns)}', 'danger')
                return redirect(url_for('employees.import_employees'))

            # Process data
            success_count = 0
            error_count = 0
            update_count = 0
            errors = []

            print(f"Processing {len(df)} rows from Excel file")
            for index, row in df.iterrows():
                try:
                    # Check if employee exists by military_id
                    if pd.isna(row['الرقم العسكري']):
                        raise ValueError('الرقم العسكري مطلوب')

                    military_id = str(row['الرقم العسكري'])
                    print(f"Processing row {index+1}: {row['الاسم']} (ID: {military_id})")

                    # Check for required fields and provide default values if missing
                    if pd.isna(row['العمل المكلف به']):
                        row['العمل المكلف به'] = 'غير محدد'

                    if pd.isna(row['الوحدة']):
                        row['الوحدة'] = 'غير محدد'

                    # Check if employee exists by military_id
                    existing_employee = Employee.query.filter_by(military_id=military_id).first()

                    # If national_id is provided, check if it's already in use by another employee
                    national_id = None
                    if 'الرقم الوطني' in df.columns and not pd.isna(row['الرقم الوطني']):
                        national_id = str(row['الرقم الوطني'])
                        existing_by_national_id = Employee.query.filter_by(national_id=national_id).first()
                        if existing_by_national_id and (not existing_employee or existing_by_national_id.id != existing_employee.id):
                            # Instead of skipping, make the national_id unique by appending a timestamp
                            print(f"National ID {national_id} already exists for employee {existing_by_national_id.name}, making it unique")
                            national_id = f"{national_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                    if existing_employee and update_existing:
                        # Update existing employee
                        existing_employee.name = row['الاسم']
                        existing_employee.department_name = row['الوحدة']
                        existing_employee.position = row['العمل المكلف به']

                        # Update optional fields if present
                        if 'الرتبة' in df.columns and not pd.isna(row['الرتبة']):
                            existing_employee.military_rank = row['الرتبة']

                        if 'الفئة' in df.columns and not pd.isna(row['الفئة']):
                            existing_employee.employee_category = row['الفئة']

                        if 'الحالة' in df.columns and not pd.isna(row['الحالة']):
                            existing_employee.employee_status = row['الحالة']

                        # Use the possibly modified national_id value from earlier check
                        if national_id:
                            existing_employee.national_id = national_id

                        if 'فصيلة الدم' in df.columns and not pd.isna(row['فصيلة الدم']):
                            existing_employee.blood_type = row['فصيلة الدم']

                        if 'تاريخ الميلاد' in df.columns and not pd.isna(row['تاريخ الميلاد']):
                            try:
                                birth_date = pd.to_datetime(row['تاريخ الميلاد'], errors='coerce')
                                if birth_date is not pd.NaT:
                                    existing_employee.date_of_birth = birth_date.date()
                            except Exception as e:
                                print(f"Error converting birth date: {e}")

                        if 'مكان الميلاد' in df.columns and not pd.isna(row['مكان الميلاد']):
                            existing_employee.birth_place = row['مكان الميلاد']

                        if 'المؤهل العلمي' in df.columns and not pd.isna(row['المؤهل العلمي']):
                            existing_employee.education = row['المؤهل العلمي']

                        if 'تاريخ المؤهل' in df.columns and not pd.isna(row['تاريخ المؤهل']):
                            try:
                                education_date = pd.to_datetime(row['تاريخ المؤهل'], errors='coerce')
                                if education_date is not pd.NaT:
                                    existing_employee.education_date = education_date.date()
                            except Exception as e:
                                print(f"Error converting education date: {e}")

                        if 'المصرف' in df.columns and not pd.isna(row['المصرف']):
                            existing_employee.bank_name = row['المصرف']

                        if 'رقم الحساب' in df.columns and not pd.isna(row['رقم الحساب']):
                            existing_employee.bank_account = row['رقم الحساب']

                        if 'رقم الهاتف' in df.columns and not pd.isna(row['رقم الهاتف']):
                            existing_employee.phone = row['رقم الهاتف']

                        # البريد الإلكتروني غير موجود في نموذج Employee
                        # لذلك نتجاهل هذا الحقل

                        if 'السكن الحالي' in df.columns and not pd.isna(row['السكن الحالي']):
                            existing_employee.address = row['السكن الحالي']

                        update_count += 1

                        # Create audit log
                        log = AuditLog(
                            user_id=current_user.id,
                            action='update',
                            entity='Employee',
                            entity_id=existing_employee.id,
                            details=f'تم تحديث بيانات الموظف {existing_employee.name} من خلال الاستيراد',
                            ip_address=request.remote_addr
                        )
                        db.session.add(log)

                    elif not existing_employee:
                        # Create new employee
                        # Handle national_id to avoid unique constraint violation
                        national_id_value = None
                        if 'الرقم الوطني' in df.columns and not pd.isna(row['الرقم الوطني']):
                            national_id_value = str(row['الرقم الوطني'])
                            # Check if national_id already exists
                            existing_national_id = Employee.query.filter_by(national_id=national_id_value).first()
                            if existing_national_id:
                                # Generate a unique national_id by appending a timestamp
                                national_id_value = f"{national_id_value}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                        # Handle military_id to avoid unique constraint violation
                        military_id_value = str(row['الرقم العسكري'])  # Convert to string to ensure consistency
                        # Check if military_id already exists (should not happen as we already checked, but just to be safe)
                        existing_military_id = Employee.query.filter_by(military_id=military_id_value).first()
                        if existing_military_id:
                            # Generate a unique military_id by appending a timestamp
                            military_id_value = f"{military_id_value}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                        # Create new employee with default values for required fields
                        new_employee = Employee(
                            name=row['الاسم'],
                            military_id=military_id_value,
                            department_name=row['الوحدة'],  # Default value already set if missing
                            position=row['العمل المكلف به'],  # Default value already set if missing
                            military_rank=row['الرتبة'] if 'الرتبة' in df.columns and not pd.isna(row['الرتبة']) else None,
                            employee_category=row['الفئة'] if 'الفئة' in df.columns and not pd.isna(row['الفئة']) else None,
                            employee_status=row['الحالة'] if 'الحالة' in df.columns and not pd.isna(row['الحالة']) else 'مستمر',
                            national_id=national_id_value,  # Using the safely checked value
                            blood_type=row['فصيلة الدم'] if 'فصيلة الدم' in df.columns and not pd.isna(row['فصيلة الدم']) else None,
                            date_of_birth=pd.to_datetime(row['تاريخ الميلاد'], errors='coerce').date() if 'تاريخ الميلاد' in df.columns and not pd.isna(row['تاريخ الميلاد']) and pd.to_datetime(row['تاريخ الميلاد'], errors='coerce') is not pd.NaT else None,
                            birth_place=row['مكان الميلاد'] if 'مكان الميلاد' in df.columns and not pd.isna(row['مكان الميلاد']) else None,
                            education=row['المؤهل العلمي'] if 'المؤهل العلمي' in df.columns and not pd.isna(row['المؤهل العلمي']) else None,
                            education_date=pd.to_datetime(row['تاريخ المؤهل'], errors='coerce').date() if 'تاريخ المؤهل' in df.columns and not pd.isna(row['تاريخ المؤهل']) and pd.to_datetime(row['تاريخ المؤهل'], errors='coerce') is not pd.NaT else None,
                            bank_name=row['المصرف'] if 'المصرف' in df.columns and not pd.isna(row['المصرف']) else None,
                            bank_account=row['رقم الحساب'] if 'رقم الحساب' in df.columns and not pd.isna(row['رقم الحساب']) else None,
                            phone=row['رقم الهاتف'] if 'رقم الهاتف' in df.columns and not pd.isna(row['رقم الهاتف']) else None,
                            # البريد الإلكتروني غير موجود في نموذج Employee
                            address=row['السكن الحالي'] if 'السكن الحالي' in df.columns and not pd.isna(row['السكن الحالي']) else None,
                            hire_date=datetime.now().date()
                        )

                        db.session.add(new_employee)
                        # Flush to get the ID
                        db.session.flush()
                        success_count += 1

                        # Create audit log
                        log = AuditLog(
                            user_id=current_user.id,
                            action='create',
                            entity='Employee',
                            entity_id=new_employee.id,
                            details=f'تم إنشاء الموظف {new_employee.name} من خلال الاستيراد',
                            ip_address=request.remote_addr
                        )
                        db.session.add(log)

                    else:
                        # Employee exists but update_existing is False
                        error_count += 1
                        errors.append(f'الموظف {row["الاسم"]} (الرقم العسكري: {row["الرقم العسكري"]}) موجود بالفعل ولم يتم تحديثه')

                except Exception as e:
                    error_count += 1
                    errors.append(f'خطأ في السطر {index + 2}: {str(e)}')

            try:
                # Try to commit the changes
                db.session.commit()
                print("Successfully committed changes to database")
            except Exception as e:
                # If there's an error, rollback the transaction
                db.session.rollback()
                print(f"Error during commit: {str(e)}")
                flash(f'حدث خطأ أثناء حفظ البيانات: {str(e)}', 'danger')
                return redirect(url_for('employees.import_employees'))

            # Show results
            print(f"Import completed: {success_count} added, {update_count} updated, {error_count} errors")
            if success_count > 0 or update_count > 0:
                message = f'تم استيراد {success_count} موظف جديد وتحديث {update_count} موظف بنجاح.'
                if error_count > 0:
                    message += f' مع {error_count} أخطاء.'
                flash(message, 'success')
            else:
                flash(f'لم يتم استيراد أي موظفين. {error_count} أخطاء.', 'warning')

            # Show errors if any
            for error in errors[:10]:  # Show only first 10 errors
                flash(error, 'danger')

            if len(errors) > 10:
                flash(f'... و {len(errors) - 10} أخطاء أخرى', 'danger')

            return redirect(url_for('employees.index'))

        except Exception as e:
            flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
            return redirect(url_for('employees.import_employees'))

    return render_template('employees/import.html', form=form)

@employees_bp.route('/download-template')
@login_required
def download_template():
    # Create a sample Excel template
    df = pd.DataFrame({
        'الاسم': ['اسم الموظف'],
        'الرقم العسكري': ['12345'],
        'الرتبة': ['ملازم'],
        'الوحدة': ['اسم الوحدة'],
        'العمل المكلف به': ['العمل المكلف به'],
        'الفئة': ['ضباط'],
        'الحالة': ['مستمر'],
        'الرقم الوطني': ['1234567890'],
        'فصيلة الدم': ['A+'],
        'تاريخ الميلاد': [datetime.now().date()],
        'مكان الميلاد': ['مكان الميلاد'],
        'المؤهل العلمي': ['المؤهل العلمي'],
        'تاريخ المؤهل': [datetime.now().date()],
        'المصرف': ['اسم المصرف'],
        'رقم الحساب': ['رقم الحساب'],
        'رقم الهاتف': ['0123456789'],
        'البريد الإلكتروني': ['<EMAIL>'],
        'السكن الحالي': ['عنوان السكن']
    })

    # Create Excel file
    output = BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='قالب الموظفين', index=False)

        # Auto-adjust columns' width
        worksheet = writer.sheets['قالب الموظفين']
        for i, col in enumerate(df.columns):
            column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
            worksheet.set_column(i, i, column_width)

    output.seek(0)

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name='employee_template.xlsx'
    )

@employees_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    employee = Employee.query.get_or_404(id)

    # Log the action before deletion
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='employee',
        entity_id=employee.id,
        details=f"تم حذف الموظف: {employee.name}",
        ip_address=request.remote_addr
    )
    db.session.add(log)

    # Delete the employee
    db.session.delete(employee)
    db.session.commit()

    flash('تم حذف الموظف بنجاح', 'success')
    return redirect(url_for('employees.index'))
