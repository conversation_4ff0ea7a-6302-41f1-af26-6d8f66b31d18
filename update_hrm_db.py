from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os
import sqlite3

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hrm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# التحقق من وجود ملف قاعدة البيانات
db_files = ['hrm.db', 'instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        try:
            # إضافة الأعمدة الجديدة باستخدام SQLite مباشرة
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # التحقق من وجود العمود hire_date
            cursor.execute("PRAGMA table_info(employee)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'hire_date' not in columns:
                print(f"إضافة عمود hire_date إلى {db_file}")
                cursor.execute("ALTER TABLE employee ADD COLUMN hire_date DATE")
            else:
                print(f"عمود hire_date موجود بالفعل في {db_file}")
                
            if 'last_promotion_date' not in columns:
                print(f"إضافة عمود last_promotion_date إلى {db_file}")
                cursor.execute("ALTER TABLE employee ADD COLUMN last_promotion_date DATE")
            else:
                print(f"عمود last_promotion_date موجود بالفعل في {db_file}")
                
            conn.commit()
            conn.close()
            print(f"تم تحديث قاعدة البيانات {db_file} بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث قاعدة البيانات {db_file}: {e}")
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

# التحقق من وجود قاعدة البيانات في مجلد instance
instance_dir = 'instance'
if os.path.exists(instance_dir):
    print(f"المجلد {instance_dir} موجود")
    for file in os.listdir(instance_dir):
        if file.endswith('.db'):
            db_path = os.path.join(instance_dir, file)
            print(f"قاعدة بيانات موجودة في المجلد instance: {db_path}")
            try:
                # إضافة الأعمدة الجديدة باستخدام SQLite مباشرة
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # التحقق من وجود العمود hire_date
                cursor.execute("PRAGMA table_info(employee)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'hire_date' not in columns:
                    print(f"إضافة عمود hire_date إلى {db_path}")
                    cursor.execute("ALTER TABLE employee ADD COLUMN hire_date DATE")
                else:
                    print(f"عمود hire_date موجود بالفعل في {db_path}")
                    
                if 'last_promotion_date' not in columns:
                    print(f"إضافة عمود last_promotion_date إلى {db_path}")
                    cursor.execute("ALTER TABLE employee ADD COLUMN last_promotion_date DATE")
                else:
                    print(f"عمود last_promotion_date موجود بالفعل في {db_path}")
                    
                conn.commit()
                conn.close()
                print(f"تم تحديث قاعدة البيانات {db_path} بنجاح")
            except Exception as e:
                print(f"خطأ في تحديث قاعدة البيانات {db_path}: {e}")
else:
    print(f"المجلد {instance_dir} غير موجود")

# التحقق من وجود قاعدة البيانات app.db
if os.path.exists('app.db'):
    print("قاعدة البيانات app.db موجودة")
    try:
        # إضافة الأعمدة الجديدة باستخدام SQLite مباشرة
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # التحقق من وجود العمود hire_date
        cursor.execute("PRAGMA table_info(employee)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'hire_date' not in columns:
            print("إضافة عمود hire_date إلى app.db")
            cursor.execute("ALTER TABLE employee ADD COLUMN hire_date DATE")
        else:
            print("عمود hire_date موجود بالفعل في app.db")
            
        if 'last_promotion_date' not in columns:
            print("إضافة عمود last_promotion_date إلى app.db")
            cursor.execute("ALTER TABLE employee ADD COLUMN last_promotion_date DATE")
        else:
            print("عمود last_promotion_date موجود بالفعل في app.db")
            
        conn.commit()
        conn.close()
        print("تم تحديث قاعدة البيانات app.db بنجاح")
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات app.db: {e}")
else:
    print("قاعدة البيانات app.db غير موجودة")
