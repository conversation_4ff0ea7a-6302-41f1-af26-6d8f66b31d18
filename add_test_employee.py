from application import create_app, db
from application.models import Employee
from datetime import datetime, date

app = create_app()

with app.app_context():
    # التحقق من عدد الموظفين قبل الإضافة
    employee_count_before = Employee.query.count()
    print(f"عدد الموظفين قبل الإضافة: {employee_count_before}")
    
    # إنشاء موظف جديد للاختبار
    test_employee = Employee(
        military_id="TEST-123",
        name="موظف اختبار",
        unit="وحدة الاختبار",
        position="منصب اختبار",
        date_of_birth=date(1990, 1, 1),
        created_at=datetime.now()
    )
    
    # إضافة الموظف إلى قاعدة البيانات
    db.session.add(test_employee)
    db.session.commit()
    
    # التحقق من عدد الموظفين بعد الإضافة
    employee_count_after = Employee.query.count()
    print(f"عدد الموظفين بعد الإضافة: {employee_count_after}")
    
    # التحقق من وجود الموظف الجديد
    new_employee = Employee.query.filter_by(military_id="TEST-123").first()
    if new_employee:
        print(f"تم إضافة الموظف بنجاح: {new_employee.name}, الرقم العسكري: {new_employee.military_id}")
    else:
        print("فشل في إضافة الموظف!")
