import sqlite3

def check_user_columns():
    """التحقق من أعمدة جدول user"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # عرض هيكل الجدول
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        
        print("أعمدة جدول user:")
        for column in columns:
            print(f"  {column[1]} ({column[2]}) {'PRIMARY KEY' if column[5] else ''}")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_user_columns()
