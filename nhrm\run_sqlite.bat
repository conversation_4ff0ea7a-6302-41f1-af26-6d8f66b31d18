@echo off
title تشغيل نظام إدارة الموارد البشرية (SQLite)
cd /d "%~dp0"

:: تعيين متغير بيئي لاستخدام SQLite
set DATABASE_URL=sqlite:///hrm.db

:: الحصول على عنوان IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found_ip
)
:found_ip
set IP=%IP:~1%

:: طباعة معلومات الاتصال
echo.
echo ===================================================
echo نظام إدارة الموارد البشرية يعمل الآن على الشبكة المحلية
echo يمكنك الوصول إلى النظام من خلال:
echo http://%IP%:5000
echo ===================================================
echo.

:: تشغيل التطبيق
python run.py

:: لا تغلق النافذة تلقائيًا
pause
