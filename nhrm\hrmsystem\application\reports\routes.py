from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from ..models import Employee, EmployeeCategory, EmployeeStatus, Permission
from .. import db
from ..decorators import permission_required
from flask import current_app
import os
import pandas as pd
from datetime import datetime
import io
import uuid

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/generate/<report_type>')
@login_required
@permission_required(Permission.VIEW_REPORTS)
def generate_report(report_type):
    """
    Generate a report based on the specified type
    """
    # Get search parameters from request args
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    category = request.args.get('category', '')
    unit = request.args.get('unit', '')

    # Build query
    query = Employee.query

    if search:
        query = query.filter(
            (Employee.name.ilike(f'%{search}%')) |
            (Employee.military_id.ilike(f'%{search}%')) |
            (Employee.national_id.ilike(f'%{search}%'))
        )

    if status:
        query = query.filter(Employee.status == status)

    if category:
        query = query.filter(Employee.category == category)

    if unit:
        query = query.filter(Employee.unit.ilike(f'%{unit}%'))

    # Order by name
    query = query.order_by(Employee.name)

    # Get all results without pagination for reports
    employees_list = query.all()

    # Prepare report data based on report type
    report_title = "الموظفين"
    report_data = {}

    if report_type == 'category':
        # Group employees by category
        report_title = "الموظفين حسب الفئة"
        categories = {}
        for employee in employees_list:
            category_name = employee.category.value if employee.category else 'غير محدد'
            if category_name not in categories:
                categories[category_name] = []
            categories[category_name].append(employee)
        report_data = categories

    elif report_type == 'unit':
        # Group employees by unit
        report_title = "الموظفين حسب الوحدة"
        units = {}
        for employee in employees_list:
            unit_name = employee.unit if employee.unit else 'غير محدد'
            if unit_name not in units:
                units[unit_name] = []
            units[unit_name].append(employee)
        report_data = units

    elif report_type == 'rank':
        # Group employees by rank
        report_title = "الموظفين حسب الرتبة"
        ranks = {}
        for employee in employees_list:
            rank_name = employee.military_rank if employee.military_rank else 'غير محدد'
            if rank_name not in ranks:
                ranks[rank_name] = []
            ranks[rank_name].append(employee)
        report_data = ranks

    elif report_type == 'status':
        # Group employees by status
        report_title = "الموظفين حسب الحالة"
        statuses = {}
        for employee in employees_list:
            status_name = employee.status.value if employee.status else 'غير محدد'
            if status_name not in statuses:
                statuses[status_name] = []
            statuses[status_name].append(employee)
        report_data = statuses

    elif report_type == 'hire_date':
        # Group employees by hire date year
        report_title = "الموظفين حسب تاريخ التعيين"
        hire_dates = {}
        for employee in employees_list:
            if employee.hire_date:
                year = employee.hire_date.year
                year_str = str(year)
                if year_str not in hire_dates:
                    hire_dates[year_str] = []
                hire_dates[year_str].append(employee)
            else:
                if 'غير محدد' not in hire_dates:
                    hire_dates['غير محدد'] = []
                hire_dates['غير محدد'].append(employee)

        # Sort the hire dates by year
        sorted_hire_dates = {}
        for year in sorted([k for k in hire_dates.keys() if k != 'غير محدد']):
            sorted_hire_dates[year] = hire_dates[year]

        # Add the 'undefined' category at the end
        if 'غير محدد' in hire_dates:
            sorted_hire_dates['غير محدد'] = hire_dates['غير محدد']

        report_data = sorted_hire_dates

    elif report_type == 'promotion_date':
        # Group employees by last promotion date year
        report_title = "الموظفين حسب تاريخ آخر ترقية"
        promotion_dates = {}
        for employee in employees_list:
            if employee.last_promotion_date:
                year = employee.last_promotion_date.year
                year_str = str(year)
                if year_str not in promotion_dates:
                    promotion_dates[year_str] = []
                promotion_dates[year_str].append(employee)
            else:
                if 'غير محدد' not in promotion_dates:
                    promotion_dates['غير محدد'] = []
                promotion_dates['غير محدد'].append(employee)

        # Sort the promotion dates by year
        sorted_promotion_dates = {}
        for year in sorted([k for k in promotion_dates.keys() if k != 'غير محدد']):
            sorted_promotion_dates[year] = promotion_dates[year]

        # Add the 'undefined' category at the end
        if 'غير محدد' in promotion_dates:
            sorted_promotion_dates['غير محدد'] = promotion_dates['غير محدد']

        report_data = sorted_promotion_dates

    else:
        # Default report - all employees
        report_title = "جميع الموظفين"
        employees = employees_list

    # Render the report template based on report type
    if report_type == 'rank':
        return render_template('reports/rank_report.html',
                            report_type=report_type,
                            report_title=report_title,
                            employees=employees_list,
                            now=datetime.now(),
                            current_user=current_user,
                            unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'))
    elif report_type == 'unit':
        return render_template('reports/unit_report.html',
                            report_type=report_type,
                            report_title=report_title,
                            employees=employees_list,
                            now=datetime.now(),
                            current_user=current_user,
                            unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'))
    elif report_type == 'category':
        return render_template('reports/category_report.html',
                            report_type=report_type,
                            report_title=report_title,
                            employees=employees_list,
                            now=datetime.now(),
                            current_user=current_user,
                            unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'))
    elif report_type == 'status':
        return render_template('reports/status_report.html',
                            report_type=report_type,
                            report_title=report_title,
                            employees=employees_list,
                            now=datetime.now(),
                            current_user=current_user,
                            unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'))
    else:
        # Default report template
        return render_template('reports/custom_report.html',
                            report_type=report_type,
                            report_title=report_title,
                            report_data=report_data,
                            employees=employees_list if report_type == 'all' else None,
                            now=datetime.now(),
                            current_user=current_user,
                            unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'))

@reports_bp.route('/export/<report_type>')
@login_required
@permission_required(Permission.EXPORT_REPORTS)
def export_report(report_type):
    """
    Export a report to Excel based on the specified type
    """
    # Get search parameters from request args
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    category = request.args.get('category', '')
    unit = request.args.get('unit', '')

    # Build query
    query = Employee.query

    if search:
        query = query.filter(
            (Employee.name.ilike(f'%{search}%')) |
            (Employee.military_id.ilike(f'%{search}%')) |
            (Employee.national_id.ilike(f'%{search}%'))
        )

    if status:
        query = query.filter(Employee.status == status)

    if category:
        query = query.filter(Employee.category == category)

    if unit:
        query = query.filter(Employee.unit.ilike(f'%{unit}%'))

    # Order by name
    query = query.order_by(Employee.name)

    # Get all results without pagination for reports
    employees_list = query.all()

    # Prepare report data based on report type
    report_title = "الموظفين"

    # Create a pandas DataFrame for the Excel file
    data = []

    if report_type in ['category', 'unit', 'rank', 'status', 'hire_date', 'promotion_date']:
        # Group employees by the specified field
        if report_type == 'category':
            report_title = "الموظفين حسب الفئة"
            groups = {}
            for employee in employees_list:
                group_name = employee.category.value if employee.category else 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

        elif report_type == 'unit':
            report_title = "الموظفين حسب الوحدة"
            groups = {}
            for employee in employees_list:
                group_name = employee.unit if employee.unit else 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

        elif report_type == 'rank':
            report_title = "الموظفين حسب الرتبة"
            groups = {}
            for employee in employees_list:
                group_name = employee.military_rank if employee.military_rank else 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

        elif report_type == 'status':
            report_title = "الموظفين حسب الحالة"
            groups = {}
            for employee in employees_list:
                group_name = employee.status.value if employee.status else 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

        elif report_type == 'hire_date':
            report_title = "الموظفين حسب تاريخ التعيين"
            groups = {}
            for employee in employees_list:
                if employee.hire_date:
                    group_name = str(employee.hire_date.year)
                else:
                    group_name = 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

            # Sort the groups by year
            sorted_groups = {}
            for year in sorted([k for k in groups.keys() if k != 'غير محدد']):
                sorted_groups[year] = groups[year]

            # Add the 'undefined' category at the end
            if 'غير محدد' in groups:
                sorted_groups['غير محدد'] = groups['غير محدد']

            groups = sorted_groups

        elif report_type == 'promotion_date':
            report_title = "الموظفين حسب تاريخ آخر ترقية"
            groups = {}
            for employee in employees_list:
                if employee.last_promotion_date:
                    group_name = str(employee.last_promotion_date.year)
                else:
                    group_name = 'غير محدد'
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(employee)

            # Sort the groups by year
            sorted_groups = {}
            for year in sorted([k for k in groups.keys() if k != 'غير محدد']):
                sorted_groups[year] = groups[year]

            # Add the 'undefined' category at the end
            if 'غير محدد' in groups:
                sorted_groups['غير محدد'] = groups['غير محدد']

            groups = sorted_groups

        # Add data to the DataFrame
        counter = 1
        for group_name, employees in groups.items():
            # Add a group header row
            data.append({
                'ت': '',
                'الرقم العسكري': '',
                'الاسم': group_name,
                'الرتبة': '',
                'الرقم الوطني': '',
                'ملاحظات': ''
            })

            # Add employee rows
            for employee in employees:
                data.append({
                    'ت': counter,
                    'الرقم العسكري': employee.military_id,
                    'الاسم': employee.name,
                    'الرتبة': employee.military_rank,
                    'الرقم الوطني': employee.national_id,
                    'ملاحظات': employee.status.value if employee.status else ''
                })
                counter += 1

    else:
        # Default report - all employees
        report_title = "جميع الموظفين"

        # Add employee rows
        for i, employee in enumerate(employees_list, 1):
            data.append({
                'ت': i,
                'الرقم العسكري': employee.military_id,
                'الاسم': employee.name,
                'الرتبة': employee.military_rank,
                'الرقم الوطني': employee.national_id,
                'ملاحظات': employee.status.value if employee.status else ''
            })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=report_title)

        # Auto-adjust columns' width
        worksheet = writer.sheets[report_title]
        for i, col in enumerate(df.columns):
            max_length = max(df[col].astype(str).map(len).max(), len(col)) + 2
            worksheet.column_dimensions[chr(65 + i)].width = max_length

    output.seek(0)

    # Generate a unique filename
    filename = f"report_{report_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
