{% extends 'layouts/base.html' %}

{% block title %}إعادة تعيين قاعدة البيانات - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .danger-zone {
        border: 2px solid #dc3545;
        border-radius: 10px;
        background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .confirmation-input {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        letter-spacing: 1px;
    }
    
    .step-indicator {
        background: #f8f9fa;
        border-left: 4px solid #dc3545;
        padding: 15px;
        margin: 10px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-header bg-danger text-white text-center">
                    <h3><i class="fas fa-exclamation-triangle"></i> منطقة خطر - إعادة تعيين قاعدة البيانات</h3>
                </div>
                <div class="card-body">
                    <div class="danger-zone p-4 mb-4">
                        <div class="text-center mb-4">
                            <i class="fas fa-database warning-icon"></i>
                            <h4 class="text-danger mt-3">تحذير: عملية خطيرة!</h4>
                        </div>
                        
                        <div class="alert alert-danger" role="alert">
                            <h5><i class="fas fa-exclamation-circle"></i> تحذير مهم:</h5>
                            <ul class="mb-0">
                                <li>ستؤدي هذه العملية إلى <strong>حذف جميع البيانات</strong> في قاعدة البيانات نهائياً</li>
                                <li>سيتم حذف جميع بيانات الموظفين والإجازات والمستخدمين</li>
                                <li>سيتم إنشاء قاعدة بيانات جديدة مع مستخدم مدير افتراضي فقط</li>
                                <li><strong>لا يمكن التراجع عن هذه العملية</strong></li>
                            </ul>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="step-indicator">
                                <h6><i class="fas fa-info-circle"></i> معلومات المستخدم الافتراضي الجديد:</h6>
                                <ul class="mb-0">
                                    <li><strong>اسم المستخدم:</strong> admin</li>
                                    <li><strong>كلمة المرور:</strong> admin123</li>
                                    <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                                    <li><strong>الدور:</strong> مدير النظام</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="step-indicator">
                                <h6><i class="fas fa-clock"></i> ما سيحدث بعد إعادة التعيين:</h6>
                                <ul class="mb-0">
                                    <li>سيتم تسجيل خروجك من النظام</li>
                                    <li>ستحتاج لتسجيل الدخول بالمستخدم الجديد</li>
                                    <li>ستحتاج لإعادة إدخال جميع البيانات</li>
                                    <li>ستحتاج لإنشاء المستخدمين من جديد</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <form method="POST" id="resetForm" class="mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-key"></i> كلمة المرور الحالية للتأكيد:
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">أدخل كلمة مرورك الحالية للتأكيد</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirmation" class="form-label">
                                        <i class="fas fa-edit"></i> اكتب "RESET_DATABASE" للتأكيد:
                                    </label>
                                    <input type="text" class="form-control confirmation-input" id="confirmation" 
                                           name="confirmation" placeholder="RESET_DATABASE" required>
                                    <div class="form-text">يجب كتابة النص بالضبط كما هو موضح</div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-secondary me-3" onclick="window.history.back()">
                                <i class="fas fa-arrow-left"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="showFinalConfirmation()">
                                <i class="fas fa-database"></i> إعادة تعيين قاعدة البيانات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتأكيد النهائي -->
<div class="modal fade" id="finalConfirmationModal" tabindex="-1" aria-labelledby="finalConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="finalConfirmationModalLabel">
                    <i class="fas fa-exclamation-triangle"></i> تأكيد نهائي
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    <h4 class="text-danger mt-3">هل أنت متأكد تماماً؟</h4>
                    <p class="lead">ستفقد جميع البيانات نهائياً ولن تتمكن من استرجاعها!</p>
                    
                    <div class="alert alert-warning mt-3">
                        <strong>آخر فرصة للتراجع!</strong><br>
                        بمجرد الضغط على "تأكيد الحذف" ستبدأ عملية حذف جميع البيانات فوراً.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="submitReset()">
                    <i class="fas fa-trash-alt"></i> تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function showFinalConfirmation() {
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('confirmation').value;
    
    if (!password) {
        alert('يرجى إدخال كلمة المرور');
        document.getElementById('password').focus();
        return;
    }
    
    if (confirmation !== 'RESET_DATABASE') {
        alert('يرجى كتابة "RESET_DATABASE" بالضبط');
        document.getElementById('confirmation').focus();
        return;
    }
    
    // إظهار النافذة المنبثقة للتأكيد النهائي
    const modal = new bootstrap.Modal(document.getElementById('finalConfirmationModal'));
    modal.show();
}

function submitReset() {
    // إظهار رسالة تحميل
    const submitBtn = document.querySelector('#finalConfirmationModal .btn-danger');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
    submitBtn.disabled = true;
    
    // إرسال النموذج
    document.getElementById('resetForm').submit();
}

// منع إرسال النموذج بالضغط على Enter
document.getElementById('resetForm').addEventListener('submit', function(e) {
    e.preventDefault();
    showFinalConfirmation();
});
</script>
{% endblock %}
