from application import create_app, db
from application.models import User
from werkzeug.security import generate_password_hash

app = create_app()

with app.app_context():
    # Check existing users
    users = User.query.all()
    print('Users in database:')
    for user in users:
        print(f'ID: {user.id}, Username: {user.username}, Email: {user.email}, Role: {user.role}, Active: {user.is_active}')
    
    # If no users, create default users
    if not users:
        print("No users found. Creating default users...")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),
            role="admin",
            is_active=True
        )
        db.session.add(admin_user)
        
        regular_user = User(
            username="user",
            email="<EMAIL>",
            password=generate_password_hash("user123"),
            role="employee",
            is_active=True
        )
        db.session.add(regular_user)
        db.session.commit()
        
        print("Default users created successfully!")
