import sqlite3
import json

def recreate_user_table():
    """إعادة إنشاء جدول user بالهيكل الصحيح"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # الحصول على بيانات المستخدمين الحالية
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print("بيانات المستخدمين قبل إعادة الإنشاء:")
        for user in users:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names[i]] = value
            print(f"  {user_data}")
        
        # إنشاء جدول مؤقت
        cursor.execute("""
            CREATE TABLE user_new (
                id INTEGER NOT NULL PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(150) NOT NULL,
                password VARCHAR(150) NOT NULL,
                role VARCHAR(50),
                created_at DATETIME,
                last_login DATETIME,
                is_active BOOLEAN,
                full_name TEXT,
                status TEXT,
                phone TEXT,
                profile_image TEXT,
                permissions TEXT
            )
        """)
        
        # نقل البيانات إلى الجدول الجديد
        for user in users:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names[i]] = value
            
            # التأكد من وجود جميع الأعمدة المطلوبة
            if 'permissions' not in user_data or user_data['permissions'] is None:
                user_data['permissions'] = '[]'
            
            cursor.execute("""
                INSERT INTO user_new (id, username, email, password, role, created_at, last_login, is_active, full_name, status, phone, profile_image, permissions)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_data['id'],
                user_data['username'],
                user_data['email'],
                user_data['password'],
                user_data['role'],
                user_data['created_at'],
                user_data['last_login'],
                user_data['is_active'],
                user_data['full_name'],
                user_data['status'],
                user_data['phone'],
                user_data['profile_image'],
                user_data['permissions']
            ))
        
        # حذف الجدول القديم
        cursor.execute("DROP TABLE user")
        
        # إعادة تسمية الجدول الجديد
        cursor.execute("ALTER TABLE user_new RENAME TO user")
        
        # إنشاء الفهارس
        cursor.execute("CREATE UNIQUE INDEX ix_user_username ON user (username)")
        cursor.execute("CREATE UNIQUE INDEX ix_user_email ON user (email)")
        
        # التحقق من البيانات بعد إعادة الإنشاء
        cursor.execute("SELECT * FROM user")
        users_new = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(user)")
        columns_new = cursor.fetchall()
        column_names_new = [column[1] for column in columns_new]
        
        print("\nبيانات المستخدمين بعد إعادة الإنشاء:")
        for user in users_new:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names_new[i]] = value
            print(f"  {user_data}")
        
        conn.commit()
        conn.close()
        
        print("\nتم إعادة إنشاء جدول user بنجاح!")
        return True
    except Exception as e:
        print(f"حدث خطأ: {e}")
        return False

if __name__ == "__main__":
    recreate_user_table()
