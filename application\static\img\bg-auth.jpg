<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0d6efd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a4bb3;stop-opacity:1" />
    </linearGradient>
    <pattern id="pattern1" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M0,30 L60,30 M30,0 L30,60" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
    </pattern>
  </defs>
  <rect width="1920" height="1080" fill="url(#grad1)"/>
  <rect width="1920" height="1080" fill="url(#pattern1)"/>
  <g opacity="0.3">
    <circle cx="300" cy="300" r="200" fill="rgba(255,255,255,0.1)"/>
    <circle cx="1600" cy="800" r="250" fill="rgba(255,255,255,0.1)"/>
    <circle cx="1000" cy="200" r="150" fill="rgba(255,255,255,0.1)"/>
    <circle cx="500" cy="900" r="180" fill="rgba(255,255,255,0.1)"/>
  </g>
</svg>
