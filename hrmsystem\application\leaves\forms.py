from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Optional, Length, ValidationError
from ..models import LeaveType
from datetime import date

class LeaveRequestForm(FlaskForm):
    employee_id = SelectField('الموظف', coerce=int, validators=[DataRequired(message='هذا الحقل مطلوب')])
    leave_type_id = SelectField('نوع الإجازة', coerce=int, validators=[DataRequired(message='هذا الحقل مطلوب')])
    start_date = DateField('تاريخ البداية', format='%Y-%m-%d', validators=[DataRequired(message='هذا الحقل مطلوب')])
    end_date = DateField('تاريخ النهاية', format='%Y-%m-%d', validators=[DataRequired(message='هذا الحقل مطلوب')])
    reason = TextAreaField('سبب الإجازة', validators=[Optional(), Length(max=500, message='يجب أن لا يتجاوز سبب الإجازة 500 حرف')])
    auto_approve = BooleanField('موافقة تلقائية')
    deduct_from_balance = BooleanField('خصم من رصيد الإجازة')
    submit = SubmitField('حفظ')
    
    def validate_end_date(self, end_date):
        if end_date.data < self.start_date.data:
            raise ValidationError('يجب أن يكون تاريخ النهاية بعد تاريخ البداية')

class LeaveTypeForm(FlaskForm):
    name = StringField('الاسم', validators=[DataRequired(message='هذا الحقل مطلوب'), Length(max=50, message='يجب أن لا يتجاوز الاسم 50 حرف')])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=200, message='يجب أن لا يتجاوز الوصف 200 حرف')])
    color = StringField('اللون', validators=[DataRequired(message='هذا الحقل مطلوب')], default='#28a745')
    submit = SubmitField('حفظ')
    
    def validate_name(self, name):
        leave_type = LeaveType.query.filter_by(name=name.data).first()
        if leave_type and (not hasattr(self, 'id') or leave_type.id != self.id.data):
            raise ValidationError('اسم نوع الإجازة مستخدم بالفعل. يرجى اختيار اسم آخر.')
