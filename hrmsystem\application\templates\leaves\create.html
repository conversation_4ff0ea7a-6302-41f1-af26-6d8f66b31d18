{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar-plus"></i> إضافة طلب إجازة جديد</h2>
        <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة الإجازات
        </a>
    </div>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-edit"></i> بيانات طلب الإجازة</h5>
        </div>
        <div class="card-body">
            <form id="leave-form" method="POST" action="{{ url_for('leaves.create') }}">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        {{ form.employee_id.label(class="form-label") }}
                        {% if form.employee_id.errors %}
                            {{ form.employee_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.employee_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.employee_id(class="form-select") }}
                        {% endif %}
                        <div class="form-text">رصيد الإجازة: <span id="leave-balance">0</span> يوم</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        {{ form.leave_type_id.label(class="form-label") }}
                        {% if form.leave_type_id.errors %}
                            {{ form.leave_type_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.leave_type_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.leave_type_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        {{ form.start_date.label(class="form-label") }}
                        {% if form.start_date.errors %}
                            {{ form.start_date(class="form-control is-invalid", type="date") }}
                            <div class="invalid-feedback">
                                {% for error in form.start_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.start_date(class="form-control", type="date") }}
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        {{ form.end_date.label(class="form-label") }}
                        {% if form.end_date.errors %}
                            {{ form.end_date(class="form-control is-invalid", type="date") }}
                            <div class="invalid-feedback">
                                {% for error in form.end_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.end_date(class="form-control", type="date") }}
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label">عدد الأيام</label>
                        <div class="form-control bg-light">
                            <span id="total-days">0</span> يوم
                        </div>
                        <div class="form-text">لا يشمل أيام العطلات وعطلات نهاية الأسبوع (الجمعة والسبت)</div>
                    </div>
                </div>
                
                <div class="mb-3">
                    {{ form.reason.label(class="form-label") }}
                    {% if form.reason.errors %}
                        {{ form.reason(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.reason.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.reason(class="form-control", rows=3) }}
                    {% endif %}
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            {{ form.auto_approve(class="form-check-input") }}
                            {{ form.auto_approve.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-check">
                            {{ form.deduct_from_balance(class="form-check-input") }}
                            {{ form.deduct_from_balance.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const totalDaysSpan = document.getElementById('total-days');
        const employeeSelect = document.getElementById('employee_id');
        const balanceSpan = document.getElementById('leave-balance');
        
        // Calculate total days when dates change
        function calculateDays() {
            if (startDateInput.value && endDateInput.value) {
                fetch(`/leaves/calculate_days?start_date=${startDateInput.value}&end_date=${endDateInput.value}`)
                    .then(response => response.json())
                    .then(data => {
                        totalDaysSpan.textContent = data.days;
                    });
            }
        }
        
        // Get employee leave balance when employee changes
        function getEmployeeBalance() {
            if (employeeSelect.value) {
                fetch(`/leaves/employee_balance/${employeeSelect.value}`)
                    .then(response => response.json())
                    .then(data => {
                        balanceSpan.textContent = data.balance;
                    });
            }
        }
        
        // Add event listeners
        if (startDateInput) startDateInput.addEventListener('change', calculateDays);
        if (endDateInput) endDateInput.addEventListener('change', calculateDays);
        if (employeeSelect) employeeSelect.addEventListener('change', getEmployeeBalance);
        
        // Calculate initial values
        if (startDateInput && startDateInput.value && endDateInput && endDateInput.value) {
            calculateDays();
        }
        
        if (employeeSelect && employeeSelect.value) {
            getEmployeeBalance();
        }
    });
</script>
{% endblock %}
