import os
import sqlite3
import json
from datetime import datetime

def backup_user_data():
    """نسخ احتياطي لبيانات المستخدمين"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # الحصول على بيانات المستخدمين
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # تحويل البيانات إلى قائمة من القواميس
        users_data = []
        for user in users:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names[i]] = value
            users_data.append(user_data)
        
        conn.close()
        return users_data
    except Exception as e:
        print(f"حدث خطأ أثناء نسخ بيانات المستخدمين: {e}")
        return []

def recreate_database(users_data):
    """إعادة إنشاء قاعدة البيانات"""
    try:
        # حذف قاعدة البيانات الحالية
        if os.path.exists('app.db'):
            os.remove('app.db')
            print("تم حذف قاعدة البيانات القديمة")
        
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE user (
                id INTEGER NOT NULL PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(150) NOT NULL,
                password VARCHAR(150) NOT NULL,
                role VARCHAR(50),
                created_at DATETIME,
                last_login DATETIME,
                active BOOLEAN,
                full_name TEXT,
                status TEXT,
                phone TEXT,
                profile_image TEXT,
                permissions TEXT
            )
        """)
        
        # إنشاء الفهارس
        cursor.execute("CREATE UNIQUE INDEX ix_user_username ON user (username)")
        cursor.execute("CREATE UNIQUE INDEX ix_user_email ON user (email)")
        
        # استعادة بيانات المستخدمين
        for user in users_data:
            cursor.execute("""
                INSERT INTO user (id, username, email, password, role, created_at, last_login, active, full_name, status, phone, profile_image, permissions)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user.get('id'),
                user.get('username'),
                user.get('email'),
                user.get('password'),
                user.get('role'),
                user.get('created_at'),
                user.get('last_login'),
                user.get('is_active', 1),  # استخدام is_active كقيمة لـ active
                user.get('full_name'),
                user.get('status'),
                user.get('phone'),
                user.get('profile_image'),
                user.get('permissions', '[]')
            ))
        
        conn.commit()
        
        # التحقق من البيانات
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print("\nبيانات المستخدمين بعد إعادة الإنشاء:")
        for user in users:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names[i]] = value
            print(f"  {user_data}")
        
        conn.close()
        print("\nتم إعادة إنشاء قاعدة البيانات بنجاح!")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إعادة إنشاء قاعدة البيانات: {e}")
        return False

def update_model_file():
    """تحديث ملف النموذج"""
    try:
        model_path = 'hrmsystem/application/models.py'
        with open(model_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # تغيير is_active إلى active
        content = content.replace("is_active = db.Column(db.Boolean, default=True)", "active = db.Column(db.Boolean, default=True)")
        
        # إضافة خاصية is_active المحسوبة
        if "@property\n    def is_active(self):" not in content:
            content = content.replace("@property\n    def is_admin(self):", "@property\n    def is_active(self):\n        return bool(self.active)\n\n    @property\n    def is_admin(self):")
        
        with open(model_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print("تم تحديث ملف النموذج بنجاح!")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث ملف النموذج: {e}")
        return False

if __name__ == "__main__":
    # نسخ احتياطي لبيانات المستخدمين
    users_data = backup_user_data()
    if users_data:
        print(f"تم نسخ بيانات {len(users_data)} مستخدم")
        
        # إعادة إنشاء قاعدة البيانات
        if recreate_database(users_data):
            # تحديث ملف النموذج
            update_model_file()
