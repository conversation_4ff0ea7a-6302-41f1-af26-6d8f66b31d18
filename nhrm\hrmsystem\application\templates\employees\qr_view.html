<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الموظف | {{ employee.name }}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            font-weight: bold;
        }
        .employee-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .employee-profile-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 0 auto 20px;
            display: block;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .info-value {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="employee-header">
            <img src="{{ url_for('static', filename='img/hrm_logo.svg') }}" alt="Logo" width="80" height="80" class="mb-3">
            <h2>بيانات الموظف</h2>
        </div>

        <div class="card mb-4">
            <div class="card-body text-center">
                {% if employee.profile_image %}
                <img src="{{ url_for('static', filename='img/employees/' + employee.profile_image) }}" alt="{{ employee.name }}" class="employee-profile-image">
                {% else %}
                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ employee.name }}" class="employee-profile-image">
                {% endif %}

                <h3 class="mb-1">{{ employee.name }}</h3>
                <p class="text-muted mb-3">{{ employee.military_rank or '' }}</p>

                <div class="mb-3">
                    <span class="badge
                        {% if employee.status.name == 'ACTIVE' %}bg-success
                        {% elif employee.status.name == 'ABSENT' %}bg-danger
                        {% elif employee.status.name == 'ASSIGNED' %}bg-info
                        {% elif employee.status.name == 'ON_LEAVE' %}bg-warning
                        {% elif employee.status.name == 'SUSPENDED' %}bg-secondary
                        {% elif employee.status.name == 'SCATTERED' %}bg-primary
                        {% elif employee.status.name == 'MEDICAL' %}bg-dark
                        {% endif %} fs-6">
                        {{ employee.status.value }}
                    </span>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-info-circle"></i> البيانات الأساسية
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="info-label">الرقم العسكري:</div>
                        <div class="info-value">{{ employee.military_id }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">الرقم الوطني:</div>
                        <div class="info-value">{{ employee.national_id or 'غير محدد' }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">فصيلة الدم:</div>
                        <div class="info-value">{{ employee.blood_type or 'غير محدد' }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">تاريخ الميلاد:</div>
                        <div class="info-value">{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير محدد' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-briefcase"></i> البيانات الوظيفية
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="info-label">الفئة:</div>
                        <div class="info-value">{{ employee.category.value if employee.category else 'غير محدد' }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">الرتبة:</div>
                        <div class="info-value">{{ employee.military_rank or 'غير محدد' }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">الوحدة:</div>
                        <div class="info-value">{{ employee.unit }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">العمل المكلف به:</div>
                        <div class="info-value">{{ employee.position }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-phone-alt"></i> بيانات الاتصال
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="info-label">رقم الهاتف:</div>
                        <div class="info-value">{{ employee.phone or 'غير محدد' }}</div>
                    </div>
                    <div class="col-6">
                        <div class="info-label">البريد الإلكتروني:</div>
                        <div class="info-value">{{ employee.email or 'غير محدد' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-muted small">تم إنشاء هذه البطاقة بواسطة نظام إدارة الموارد البشرية</p>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
