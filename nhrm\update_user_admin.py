import sqlite3

def update_user_admin_column():
    """تحديث جدول المستخدمين لإضافة عمود is_admin"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # التحقق من وجود عمود is_admin
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'is_admin' not in column_names:
            print("إضافة عمود is_admin إلى جدول user...")
            cursor.execute("ALTER TABLE user ADD COLUMN is_admin BOOLEAN DEFAULT 0")
            
            # تحديث قيمة is_admin لجميع المستخدمين بناءً على قيمة role
            cursor.execute("UPDATE user SET is_admin = 1 WHERE role = 'ADMIN' OR role = 'admin'")
            
            print("تم تحديث جدول user بنجاح!")
        else:
            print("عمود is_admin موجود بالفعل في جدول user")
        
        conn.commit()
        
        # التحقق من بيانات المستخدمين
        cursor.execute("SELECT id, username, role, is_admin FROM user")
        users = cursor.fetchall()
        
        print("\nبيانات المستخدمين بعد التحديث:")
        for user in users:
            print(f"  ID: {user[0]}, Username: {user[1]}, Role: {user[2]}, Is Admin: {user[3]}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث جدول user: {e}")
        return False

if __name__ == "__main__":
    update_user_admin_column()
