import sqlite3
import os

def fix_audit_log(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_log'")
        if cursor.fetchone():
            print(f"جدول audit_log موجود في {db_path}")
            
            # الحصول على معرف المستخدم الأول (المدير)
            cursor.execute("SELECT id FROM user WHERE is_admin = 1 OR role = 'ADMIN' LIMIT 1")
            admin_id = cursor.fetchone()
            
            if not admin_id:
                cursor.execute("SELECT id FROM user LIMIT 1")
                admin_id = cursor.fetchone()
            
            if admin_id:
                admin_id = admin_id[0]
                print(f"تم العثور على معرف المستخدم المدير: {admin_id}")
                
                # البحث عن السجلات التي تحتوي على قيم NULL في عمود user_id
                cursor.execute("SELECT id FROM audit_log WHERE user_id IS NULL")
                null_records = cursor.fetchall()
                
                if null_records:
                    null_ids = [record[0] for record in null_records]
                    print(f"تم العثور على {len(null_ids)} سجل بدون قيمة user_id: {null_ids}")
                    
                    # تحديث السجلات لتعيين قيمة المستخدم المدير
                    cursor.execute("UPDATE audit_log SET user_id = ? WHERE user_id IS NULL", (admin_id,))
                    conn.commit()
                    print(f"تم تحديث {cursor.rowcount} سجل بنجاح")
                else:
                    print("لا توجد سجلات بدون قيمة user_id")
            else:
                print("لم يتم العثور على أي مستخدم في النظام")
        else:
            print(f"جدول audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في إصلاح جدول audit_log في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['app.db', 'instance/app.db', 'instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        fix_audit_log(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من إصلاح جدول audit_log")
