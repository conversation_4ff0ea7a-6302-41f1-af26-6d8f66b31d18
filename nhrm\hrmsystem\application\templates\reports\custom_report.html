{% extends 'layouts/base_print.html' %}

{% block content %}
<div class="report-container">
    <!-- أزرار التحكم (تظهر فقط في الشاشة وليس عند الطباعة) -->
    <div class="d-flex justify-content-between mb-4 no-print">
        <div>
            <button class="btn btn-primary btn-print">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
            <a href="{{ url_for('reports.export_report', report_type=report_type, **request.args) }}" class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
            </a>
        </div>
        <button class="btn btn-secondary btn-back">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </button>
    </div>

    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="report-title">وزارة الداخلية</div>
        <div class="report-subtitle">مديرية أمن {{ unit_name|default('الخمس') }}</div>
        <div class="report-subtitle">قسم المرور والتراخيص {{ unit_name|default('الخمس') }}</div>
        <div class="mt-4">كشف عن: {{ report_title|default('...............') }}</div>
    </div>

    <!-- جدول التقرير -->
    <table class="report-table">
        <thead>
            <tr>
                <th width="5%">ت</th>
                <th width="15%">الرقم العسكري</th>
                <th width="30%">الاســــــــــــم</th>
                <th width="15%">الرتبـــة</th>
                <th width="15%">الرقم الوطنـــي</th>
                <th width="20%">ملاحظـــات</th>
            </tr>
        </thead>
        <tbody>
            {% if report_data %}
                {% set counter = 1 %}
                {% for category, employees in report_data.items() %}
                    <tr>
                        <td colspan="6" class="category-header">{{ category }}</td>
                    </tr>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ counter }}</td>
                        <td>{{ employee.military_id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.military_rank }}</td>
                        <td>{{ employee.national_id }}</td>
                        <td>{{ employee.status.value if employee.status else '' }}</td>
                    </tr>
                    {% set counter = counter + 1 %}
                    {% endfor %}
                {% endfor %}
            {% elif employees %}
                {% for employee in employees %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ employee.military_id }}</td>
                    <td>{{ employee.name }}</td>
                    <td>{{ employee.military_rank }}</td>
                    <td>{{ employee.national_id }}</td>
                    <td>{{ employee.status.value if employee.status else '' }}</td>
                </tr>
                {% endfor %}
            {% else %}
                {% for i in range(1, 11) %}
                <tr>
                    <td>{{ i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- تذييل التقرير -->
    <div class="report-footer">
        <div>
            <p>يعتمد</p>
        </div>
        <div>
            <p>التاريخ: {{ now.strftime('%Y/%m/%d') }}</p>
            <p>اسم المستخدم: {{ current_user.full_name or current_user.username }}</p>
        </div>
    </div>

    <!-- رقم الصفحة -->
    <div class="page-number">
        {{ page_number|default('1') }}
    </div>
</div>
{% endblock %}
