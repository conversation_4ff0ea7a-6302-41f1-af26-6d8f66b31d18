import sqlite3
import os
import sys

def fix_audit_log(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_log'")
        if cursor.fetchone():
            print(f"جدول audit_log موجود في {db_path}")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(audit_log)")
            columns = cursor.fetchall()
            print("هيكل جدول audit_log:")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            # الحصول على معرف المستخدم الأول
            cursor.execute("SELECT id FROM user LIMIT 1")
            user_id = cursor.fetchone()
            
            if user_id:
                user_id = user_id[0]
                print(f"تم العثور على معرف المستخدم: {user_id}")
                
                # عرض السجلات التي تحتوي على قيم NULL في عمود user_id
                cursor.execute("SELECT * FROM audit_log WHERE user_id IS NULL")
                null_records = cursor.fetchall()
                
                if null_records:
                    print(f"تم العثور على {len(null_records)} سجل بدون قيمة user_id:")
                    for record in null_records:
                        print(f"  ID: {record[0]}, Action: {record[2]}, Entity: {record[3]}, Entity ID: {record[4]}")
                    
                    # تحديث السجلات لتعيين قيمة المستخدم
                    cursor.execute("UPDATE audit_log SET user_id = ? WHERE user_id IS NULL", (user_id,))
                    conn.commit()
                    print(f"تم تحديث {cursor.rowcount} سجل بنجاح")
                    
                    # التحقق من نجاح التحديث
                    cursor.execute("SELECT COUNT(*) FROM audit_log WHERE user_id IS NULL")
                    null_count = cursor.fetchone()[0]
                    if null_count == 0:
                        print("تم إصلاح جميع السجلات بنجاح")
                    else:
                        print(f"لا يزال هناك {null_count} سجل بدون قيمة user_id")
                else:
                    print("لا توجد سجلات بدون قيمة user_id")
                
                # عرض السجلات المحددة
                if len(sys.argv) > 1 and sys.argv[1] == "--show-records":
                    record_ids = [414, 415, 418, 420]  # الأرقام المذكورة في رسالة الخطأ
                    for record_id in record_ids:
                        cursor.execute("SELECT * FROM audit_log WHERE id = ?", (record_id,))
                        record = cursor.fetchone()
                        if record:
                            print(f"السجل {record_id}: {record}")
                        else:
                            print(f"لم يتم العثور على السجل {record_id}")
            else:
                print("لم يتم العثور على أي مستخدم في النظام")
        else:
            print(f"جدول audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في إصلاح جدول audit_log في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        fix_audit_log(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من إصلاح جدول audit_log")
