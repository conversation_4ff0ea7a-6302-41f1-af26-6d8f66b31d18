<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة الموظف - {{ employee.name }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        @page {
            size: A4;
            margin: 0.5cm;
        }

        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
                padding: 0;
                margin: 0;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }

            .no-print {
                display: none !important;
            }

            .card {
                border: none !important;
                box-shadow: none !important;
            }

            .row {
                page-break-inside: avoid;
            }

            .employee-info {
                padding: 10px !important;
            }

            .info-section {
                margin-bottom: 10px !important;
            }

            .info-item {
                margin-bottom: 5px !important;
            }

            .employee-header {
                padding: 10px !important;
            }

            .employee-photo {
                width: 100px !important;
                height: 100px !important;
            }

            .print-footer {
                margin-top: 10px !important;
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            font-size: 14px;
        }

        .employee-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 210mm;
            margin: 0 auto;
        }

        .employee-header {
            background-color: #007bff;
            color: white;
            padding: 15px;
            position: relative;
        }

        .employee-photo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .employee-info {
            padding: 15px;
        }

        .info-section {
            margin-bottom: 15px;
        }

        .info-title {
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 10px;
            color: #007bff;
            font-size: 16px;
        }

        .info-item {
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 13px;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .qr-code {
            text-align: center;
            margin-top: 10px;
        }

        .qr-code img {
            max-width: 100px;
        }

        .print-footer {
            text-align: center;
            margin-top: 15px;
            font-size: 0.7rem;
            color: #6c757d;
        }

        .compact-table {
            margin-bottom: 0;
        }

        .compact-table td, .compact-table th {
            padding: 0.3rem;
            font-size: 13px;
        }

        h4 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        p {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4 mb-5">
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right ml-1"></i> العودة
                    </a>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print ml-1"></i> طباعة
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="employee-card">
                    <div class="employee-header">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="80" height="80" class="mb-2">
                            </div>
                            <div class="col-md-3 text-center">
                                {% if employee.profile_image %}
                                <img src="{{ url_for('static', filename='img/' + employee.profile_image) }}" alt="{{ employee.name }}" class="employee-photo">
                                {% else %}
                                <img src="{{ url_for('static', filename='img/default-profile.png') }}" alt="{{ employee.name }}" class="employee-photo">
                                {% endif %}
                            </div>
                            <div class="col-md-7">
                                <h4>{{ employee.name }}</h4>
                                <p class="mb-1"><strong>الرقم العسكري:</strong> {{ employee.military_id }}</p>
                                <p class="mb-1"><strong>الرتبة:</strong> {{ employee.military_rank }}</p>
                                <p class="mb-1"><strong>الوحدة:</strong> {{ employee.department_name }}</p>
                                <p class="mb-0">
                                    <strong>الحالة:</strong>
                                    {% if employee.employee_status == 'مستمر' %}
                                        <span class="status-badge bg-success text-white">مستمر</span>
                                    {% elif employee.employee_status == 'غياب وهروب' %}
                                        <span class="status-badge bg-danger text-white">غياب وهروب</span>
                                    {% elif employee.employee_status == 'مكلف' %}
                                        <span class="status-badge bg-primary text-white">مكلف</span>
                                    {% elif employee.employee_status == 'اجازة' %}
                                        <span class="status-badge bg-info text-white">اجازة</span>
                                    {% elif employee.employee_status == 'ايقاف عن العمل' %}
                                        <span class="status-badge bg-warning text-dark">ايقاف عن العمل</span>
                                    {% elif employee.employee_status == 'متفرق' %}
                                        <span class="status-badge bg-secondary text-white">متفرق</span>
                                    {% elif employee.employee_status == 'عيادة طبية' %}
                                        <span class="status-badge bg-info text-white">عيادة طبية</span>
                                    {% else %}
                                        <span class="status-badge bg-secondary text-white">غير محدد</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="employee-info">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h4 class="info-title"><i class="fas fa-user me-2"></i>المعلومات الشخصية</h4>
                                    <table class="table table-sm compact-table">
                                        <tr>
                                            <td><span class="info-label">الرقم الوطني</span></td>
                                            <td>{{ employee.national_id or 'غير متوفر' }}</td>
                                            <td><span class="info-label">فصيلة الدم</span></td>
                                            <td>{{ employee.blood_type or 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <td><span class="info-label">تاريخ الميلاد</span></td>
                                            <td>{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير متوفر' }}</td>
                                            <td><span class="info-label">مكان الميلاد</span></td>
                                            <td>{{ employee.birth_place or 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <td><span class="info-label">السكن الحالي</span></td>
                                            <td colspan="3">{{ employee.address or 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <td><span class="info-label">رقم الهاتف</span></td>
                                            <td>{{ employee.phone or 'غير متوفر' }}</td>
                                            <td><span class="info-label">البريد الإلكتروني</span></td>
                                            <td>{{ employee.email or 'غير متوفر' }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="info-section">
                                    <h4 class="info-title"><i class="fas fa-graduation-cap me-2"></i>المعلومات التعليمية</h4>
                                    <table class="table table-sm compact-table">
                                        <tr>
                                            <td><span class="info-label">المؤهل العلمي</span></td>
                                            <td>{{ employee.education or 'غير متوفر' }}</td>
                                            <td><span class="info-label">تاريخ الحصول على المؤهل</span></td>
                                            <td>{{ employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير متوفر' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-section">
                                    <h4 class="info-title"><i class="fas fa-briefcase me-2"></i>المعلومات الوظيفية</h4>
                                    <table class="table table-sm compact-table">
                                        <tr>
                                            <td><span class="info-label">الفئة</span></td>
                                            <td>{{ employee.employee_category or 'غير محدد' }}</td>
                                            <td><span class="info-label">العمل المكلف به</span></td>
                                            <td>{{ employee.position }}</td>
                                        </tr>
                                        <tr>
                                            <td><span class="info-label">تاريخ التعيين</span></td>
                                            <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                                            <td><span class="info-label">تاريخ آخر ترقية</span></td>
                                            <td>{{ employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير متوفر' }}</td>
                                        </tr>
                                        {% if employee.status_notes %}
                                        <tr>
                                            <td><span class="info-label">ملاحظات حول الحالة</span></td>
                                            <td colspan="3">{{ employee.status_notes }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>

                                <div class="info-section">
                                    <h4 class="info-title"><i class="fas fa-university me-2"></i>المعلومات المالية</h4>
                                    <table class="table table-sm compact-table">
                                        <tr>
                                            <td><span class="info-label">المصرف</span></td>
                                            <td>{{ employee.bank_name or 'غير متوفر' }}</td>
                                            <td><span class="info-label">رقم حساب المصرف</span></td>
                                            <td>{{ employee.bank_account or 'غير متوفر' }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="info-section">
                                    <h4 class="info-title"><i class="fas fa-calendar-alt me-2"></i>رصيد الإجازة</h4>
                                    <table class="table table-sm compact-table">
                                        <tr>
                                            <td><span class="info-label">رصيد الإجازة السنوية</span></td>
                                            <td>{{ leave_balance }} يوم</td>
                                            <td><span class="info-label">الإجازات المستخدمة</span></td>
                                            <td>{{ used_leave }} يوم</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 text-center">
                                <div class="qr-code">
                                    <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid">
                                    <p class="mt-2">امسح الرمز لعرض بيانات الموظف مباشرة</p>
                                </div>
                            </div>
                        </div>

                        <div class="print-footer">
                            <p>تم طباعة هذه البطاقة في {{ now().strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
