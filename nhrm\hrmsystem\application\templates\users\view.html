{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user"></i> بيانات المستخدم</h2>
        <div>
            {% if current_user.is_admin or current_user.role == 'admin' or (current_user.role is defined and current_user.role.name == 'ADMIN') or current_user.id == user.id %}
            <a href="{{ url_for('users.edit', id=user.id) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل البيانات
            </a>
            {% endif %}
            {% if (current_user.is_admin or current_user.role == 'admin' or (current_user.role is defined and current_user.role.name == 'ADMIN')) and user.id != current_user.id and user.role != 'admin' and (user.role is not defined or user.role.name != 'ADMIN') %}
            <a href="{{ url_for('users.manage_permissions', id=user.id) }}" class="btn btn-info">
                <i class="fas fa-key"></i> إدارة الصلاحيات
            </a>
            {% endif %}
            <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى قائمة المستخدمين
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-circle"></i> البيانات الشخصية</h5>
                </div>
                <div class="card-body text-center">
                    {% if user.profile_image %}
                    <img src="{{ url_for('static', filename='img/users/' + user.profile_image) }}" alt="{{ user.username }}" class="img-thumbnail rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                    <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ user.username }}" class="img-thumbnail rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% endif %}
                    <h4>{{ user.full_name or user.username }}</h4>
                    <p class="text-muted">{{ user.username }}</p>

                    <div class="mt-3">
                        {% if user.role == 'admin' or (user.role is defined and user.role.name == 'ADMIN') %}
                        <span class="badge bg-danger">مدير النظام</span>
                        {% elif user.role == 'supervisor' or (user.role is defined and user.role.name == 'SUPERVISOR') %}
                        <span class="badge bg-warning">مشرف</span>
                        {% else %}
                        <span class="badge bg-info">مستخدم عادي</span>
                        {% endif %}

                        {% if user.status == 'active' or (user.status is defined and user.status.name == 'ACTIVE') %}
                        <span class="badge bg-success">نشط</span>
                        {% elif user.status == 'inactive' or (user.status is defined and user.status.name == 'INACTIVE') %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% else %}
                        <span class="badge bg-danger">موقوف</span>
                        {% endif %}
                    </div>
                </div>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-envelope text-primary"></i> البريد الإلكتروني: {{ user.email }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-phone text-primary"></i> رقم الهاتف: {{ user.phone or 'غير محدد' }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-calendar-alt text-primary"></i> تاريخ الإنشاء: {{ user.created_at.strftime('%Y-%m-%d') }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-clock text-primary"></i> آخر تحديث: {{ user.updated_at.strftime('%Y-%m-%d %H:%M') }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-sign-in-alt text-primary"></i> آخر تسجيل دخول:
                        {% if user.last_login %}
                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        لم يسجل الدخول بعد
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>

        <!-- User Activity -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> سجل النشاط</h5>
                </div>
                <div class="card-body">
                    {% if audit_logs.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النشاط</th>
                                    <th>بواسطة</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in audit_logs.items %}
                                <tr>
                                    <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if log.action == 'create' %}
                                        <span class="badge bg-success">إنشاء حساب</span>
                                        {% elif log.action == 'update' %}
                                        <span class="badge bg-warning">تحديث البيانات</span>
                                        {% elif log.action == 'delete' %}
                                        <span class="badge bg-danger">حذف الحساب</span>
                                        {% elif log.action == 'login' %}
                                        <span class="badge bg-info">تسجيل دخول</span>
                                        {% elif log.action == 'logout' %}
                                        <span class="badge bg-secondary">تسجيل خروج</span>
                                        {% elif log.action == 'password_change' %}
                                        <span class="badge bg-primary">تغيير كلمة المرور</span>
                                        {% elif log.action == 'profile_update' %}
                                        <span class="badge bg-info">تحديث الملف الشخصي</span>
                                        {% else %}
                                        <span class="badge bg-dark">{{ log.action }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.actor.username }}</td>
                                    <td>
                                        {% if log.action == 'update' and log.old_values and log.new_values %}
                                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#changes{{ log.id }}" aria-expanded="false">
                                            عرض التغييرات
                                        </button>
                                        <div class="collapse mt-2" id="changes{{ log.id }}">
                                            <div class="card card-body">
                                                <h6>القيم القديمة:</h6>
                                                <pre class="mb-3">{{ log.old_values }}</pre>
                                                <h6>القيم الجديدة:</h6>
                                                <pre>{{ log.new_values }}</pre>
                                            </div>
                                        </div>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if audit_logs.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.view', id=user.id, page=audit_logs.prev_num) }}">السابق</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                            </li>
                            {% endif %}

                            {% for page_num in audit_logs.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == audit_logs.page %}
                                    <li class="page-item active" aria-current="page">
                                        <a class="page-link" href="{{ url_for('users.view', id=user.id, page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('users.view', id=user.id, page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if audit_logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.view', id=user.id, page=audit_logs.next_num) }}">التالي</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p class="mb-0">لا توجد سجلات نشاط لهذا المستخدم.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
