import sqlite3

def check_user():
    """التحقق من بيانات المستخدم في قاعدة البيانات"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # عرض بيانات المستخدم
        cursor.execute("SELECT * FROM user WHERE id = 1")
        user = cursor.fetchone()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print("بيانات المستخدم:")
        for i, value in enumerate(user):
            print(f"  {column_names[i]}: {value}")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_user()
