import os
from datetime import timedelta

class Config:
    # Secret key for session management
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'

    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///hrm.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)

    # Upload folder for employee photos
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'application/static/uploads')

    # Maximum file size for uploads (5MB)
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024

    # Default admin user
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = 'admin123'
    ADMIN_EMAIL = '<EMAIL>'

    # Pagination
    EMPLOYEES_PER_PAGE = 10
    LEAVES_PER_PAGE = 10
    AUDIT_LOGS_PER_PAGE = 20
    USERS_PER_PAGE = 10

    # Weekend days (0 = Monday, 6 = Sunday)
    WEEKEND_DAYS = [4, 5]  # Friday and Saturday
