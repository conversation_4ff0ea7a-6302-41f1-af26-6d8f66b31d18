{% extends 'layouts/base.html' %}

{% block title %}إدارة الإجازات - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.css">
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="40" height="40" class="me-2">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-alt me-2"></i>إدارة الإجازات
            </h5>
        </div>
        <div>
            <a href="{{ url_for('leaves.calendar_view') }}" class="btn btn-light btn-sm me-2">
                <i class="fas fa-calendar me-1"></i>عرض التقويم
            </a>
            <a href="{{ url_for('leaves.create') }}" class="btn btn-light btn-sm">
                <i class="fas fa-plus me-1"></i>طلب إجازة جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Search Form -->
        <form method="GET" action="{{ url_for('leaves.index') }}" class="mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    {{ form.employee.label(class="form-label") }}
                    {{ form.employee(class="form-select") }}
                </div>
                <div class="col-md-3">
                    {{ form.leave_type.label(class="form-label") }}
                    {{ form.leave_type(class="form-select") }}
                </div>
                <div class="col-md-2">
                    {{ form.status.label(class="form-label") }}
                    {{ form.status(class="form-select") }}
                </div>
                <div class="col-md-2">
                    {{ form.date_from.label(class="form-label") }}
                    {{ form.date_from(class="form-control date-picker", placeholder="من تاريخ", value=date_from) }}
                </div>
                <div class="col-md-2">
                    {{ form.date_to.label(class="form-label") }}
                    {{ form.date_to(class="form-control date-picker", placeholder="إلى تاريخ", value=date_to) }}
                </div>
                <div class="col-12 d-flex justify-content-end">
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </div>
        </form>

        <!-- Leaves Table -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>نوع الإجازة</th>
                        <th>من تاريخ</th>
                        <th>إلى تاريخ</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in leave_requests.items %}
                    <tr>
                        <td>{{ leave.id }}</td>
                        <td>{{ leave.employee_rel.name }}</td>
                        <td>{{ leave.leave_type_rel.name }}</td>
                        <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ leave.total_days }}</td>
                        <td>
                            {% if leave.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status == 'approved' %}
                            <span class="badge bg-success">تمت الموافقة</span>
                            {% elif leave.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('leaves.view', id=leave.id) }}" class="btn btn-sm btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.role == 'admin' and leave.status == 'pending' %}
                                <a href="{{ url_for('leaves.approve', id=leave.id) }}" class="btn btn-sm btn-success" title="الموافقة/الرفض">
                                    <i class="fas fa-check-circle"></i>
                                </a>
                                {% endif %}
                                {% if current_user.role == 'admin' %}
                                <button type="button" class="btn btn-sm btn-danger" title="حذف" data-bs-toggle="modal" data-bs-target="#deleteModal{{ leave.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ leave.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ leave.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteModalLabel{{ leave.id }}">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من حذف طلب الإجازة للموظف <strong>{{ leave.employee_rel.name }}</strong>؟
                                            <br>
                                            <span class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</span>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('leaves.delete', id=leave.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center">لا توجد طلبات إجازة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if leave_requests.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if leave_requests.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('leaves.index', page=leave_requests.prev_num, employee=employee_id, leave_type=leave_type_id, status=status_id, date_from=date_from, date_to=date_to) }}">السابق</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                </li>
                {% endif %}

                {% for page_num in leave_requests.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page_num %}
                        {% if page_num == leave_requests.page %}
                        <li class="page-item active" aria-current="page">
                            <a class="page-link" href="#">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('leaves.index', page=page_num, employee=employee_id, leave_type=leave_type_id, status=status_id, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if leave_requests.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('leaves.index', page=leave_requests.next_num, employee=employee_id, leave_type=leave_type_id, status=status_id, date_from=date_from, date_to=date_to) }}">التالي</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date pickers
        flatpickr(".date-picker", {
            locale: "ar",
            dateFormat: "Y-m-d",
            allowInput: true
        });

        // Set selected values for dropdowns
        const employeeSelect = document.getElementById('employee');
        const leaveTypeSelect = document.getElementById('leave_type');
        const statusSelect = document.getElementById('status');

        if (employeeSelect) {
            employeeSelect.value = "{{ employee_id or 0 }}";
        }

        if (leaveTypeSelect) {
            leaveTypeSelect.value = "{{ leave_type_id or 0 }}";
        }

        if (statusSelect) {
            statusSelect.value = "{{ status_id or 0 }}";
        }
    });
</script>
{% endblock %}
