import os
import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime

def reset_database():
    """حذف قاعدة البيانات الحالية وإنشاء قاعدة بيانات جديدة"""
    try:
        # حذف قاعدة البيانات الحالية
        if os.path.exists('app.db'):
            os.remove('app.db')
            print("تم حذف قاعدة البيانات القديمة")
        
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين بهيكل بسيط
        cursor.execute("""
            CREATE TABLE user (
                id INTEGER NOT NULL PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                email VARCHAR(150) NOT NULL,
                password VARCHAR(150) NOT NULL,
                role VARCHAR(50),
                created_at DATETIME,
                last_login DATETIME,
                full_name TEXT,
                status TEXT,
                phone TEXT,
                profile_image TEXT
            )
        """)
        
        # إنشاء الفهارس
        cursor.execute("CREATE UNIQUE INDEX ix_user_username ON user (username)")
        cursor.execute("CREATE UNIQUE INDEX ix_user_email ON user (email)")
        
        # إنشاء مستخدم افتراضي (admin/admin123)
        admin_password = generate_password_hash('admin123')
        now = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO user (id, username, email, password, role, created_at, full_name, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            1, 
            'admin', 
            '<EMAIL>', 
            admin_password, 
            'admin', 
            now,
            'مدير النظام',
            'active'
        ))
        
        # إنشاء جداول أخرى ضرورية
        # جدول الموظفين
        cursor.execute("""
            CREATE TABLE employee (
                id INTEGER NOT NULL PRIMARY KEY,
                military_id VARCHAR(20) NOT NULL,
                name VARCHAR(100) NOT NULL,
                national_id VARCHAR(20),
                blood_type VARCHAR(10),
                date_of_birth DATE,
                birth_place VARCHAR(100),
                current_residence VARCHAR(200),
                education VARCHAR(100),
                education_date DATE,
                category VARCHAR(50),
                bank_name VARCHAR(100),
                bank_account VARCHAR(50),
                military_rank VARCHAR(50),
                unit VARCHAR(100),
                position VARCHAR(100),
                status VARCHAR(50),
                status_notes TEXT,
                phone VARCHAR(20),
                email VARCHAR(120),
                profile_image VARCHAR(255),
                leave_balance INTEGER,
                hire_date DATE,
                last_promotion_date DATE,
                created_at DATETIME,
                updated_at DATETIME
            )
        """)
        
        # جدول سجلات التدقيق
        cursor.execute("""
            CREATE TABLE audit_log (
                id INTEGER NOT NULL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                action VARCHAR(50) NOT NULL,
                entity VARCHAR(50) NOT NULL,
                entity_id INTEGER,
                details TEXT,
                ip_address VARCHAR(50),
                timestamp DATETIME,
                FOREIGN KEY(user_id) REFERENCES user(id)
            )
        """)
        
        # جدول سجلات تدقيق المستخدمين
        cursor.execute("""
            CREATE TABLE user_audit_log (
                id INTEGER NOT NULL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                action_by INTEGER NOT NULL,
                action VARCHAR(50) NOT NULL,
                old_values TEXT,
                new_values TEXT,
                ip_address VARCHAR(50),
                user_agent VARCHAR(255),
                timestamp DATETIME,
                FOREIGN KEY(user_id) REFERENCES user(id),
                FOREIGN KEY(action_by) REFERENCES user(id)
            )
        """)
        
        conn.commit()
        
        # التحقق من البيانات
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print("\nبيانات المستخدمين بعد إعادة الإنشاء:")
        for user in users:
            user_data = {}
            for i, value in enumerate(user):
                user_data[column_names[i]] = value
            print(f"  {user_data}")
        
        conn.close()
        print("\nتم إعادة إنشاء قاعدة البيانات بنجاح!")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إعادة إنشاء قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    reset_database()
