import sqlite3

def check_db_structure():
    """التحقق من هيكل قاعدة البيانات بالتفصيل"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("جداول قاعدة البيانات:")
        for table in tables:
            table_name = table[0]
            print(f"\n=== جدول {table_name} ===")
            
            # عرض هيكل الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("الأعمدة:")
            for column in columns:
                print(f"  {column[1]} ({column[2]}) {'PRIMARY KEY' if column[5] else ''}")
            
            # عرض عدد الصفوف
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"عدد الصفوف: {count}")
            
            # عرض بيانات الصف الأول (إذا وجد)
            if count > 0 and table_name == 'user':
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                row = cursor.fetchone()
                column_names = [description[0] for description in cursor.description]
                
                print("بيانات الصف الأول:")
                for i, value in enumerate(row):
                    print(f"  {column_names[i]}: {value}")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_db_structure()
