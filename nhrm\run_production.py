from application import create_app, db
from application.models import Employee, LeaveRequest, User
from datetime import datetime, timezone
from werkzeug.security import generate_password_hash
from waitress import serve
import socket

app = create_app()

# Create admin user if not exists
with app.app_context():
    # Create tables
    db.create_all()

    # Check if admin user exists
    admin_exists = User.query.filter_by(username="admin").first()
    if not admin_exists:
        print("Creating admin user...")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),  # تشفير كلمة المرور
            role="admin",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(admin_user)
        db.session.commit()
        print("Admin user created successfully!")
    else:
        print("Admin user already exists.")

# Add template context processors
@app.context_processor
def inject_globals():
    return {
        'now': datetime.now(),
        'employee_count': Employee.query.count(),
        'pending_leaves': LeaveRequest.query.filter_by(status='pending').count(),
        'approved_leaves': LeaveRequest.query.filter_by(status='approved').count(),
        'user_count': User.query.count()
    }

if __name__ == "__main__":
    # الحصول على عنوان IP المحلي للجهاز
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)

    # طباعة معلومات الاتصال
    print("=" * 50)
    print(f"نظام إدارة الموارد البشرية يعمل الآن على الشبكة المحلية")
    print(f"يمكنك الوصول إلى النظام من خلال:")
    print(f"http://{local_ip}:8080")
    print(f"أو")
    print(f"http://localhost:8080")
    print("=" * 50)

    # تشغيل الخادم باستخدام Waitress (أكثر أماناً للإنتاج)
    serve(app, host='0.0.0.0', port=8080, threads=4)
