from flask import Blueprint, render_template
from flask_login import login_required, current_user
from .. import db
from ..models import Employee, LeaveRequest, Department, User
from sqlalchemy import func
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get total employee count
    total_employees = Employee.query.count()

    # Get employee count by status
    status_counts = db.session.query(
        Employee.employee_status,
        func.count(Employee.id)
    ).group_by(Employee.employee_status).all()

    # Convert to dict for easier access
    status_dict = {status: count for status, count in status_counts}

    # Get employee count by category
    category_counts = db.session.query(
        Employee.employee_category,
        func.count(Employee.id)
    ).group_by(Employee.employee_category).all()

    # Convert to dict for easier access
    category_dict = {category: count for category, count in category_counts}

    # Get employee count by department (now using department_name)
    dept_counts = db.session.query(
        Employee.department_name,
        func.count(Employee.id)
    ).group_by(Employee.department_name).all()

    dept_names = [d[0] for d in dept_counts]
    dept_values = [d[1] for d in dept_counts]

    # Get employee count by military rank
    rank_counts = db.session.query(
        Employee.military_rank,
        func.count(Employee.id)
    ).group_by(Employee.military_rank).all()

    rank_names = [r[0] for r in rank_counts if r[0]]  # Filter out None values
    rank_values = [r[1] for r in rank_counts if r[0]]

    # Get leave requests by month (last 6 months)
    today = datetime.now()
    six_months_ago = today - timedelta(days=180)

    monthly_leaves = db.session.query(
        func.strftime('%Y-%m', LeaveRequest.start_date).label('month'),
        func.count(LeaveRequest.id).label('count'),
        LeaveRequest.status
    ).filter(LeaveRequest.start_date >= six_months_ago)\
     .group_by('month', LeaveRequest.status).all()

    # Process monthly data
    months_data = {}
    for month, count, status in monthly_leaves:
        if not month:
            continue
        if month not in months_data:
            months_data[month] = {'approved': 0, 'rejected': 0, 'pending': 0}
        months_data[month][status] = count

    # Sort by month
    sorted_months = sorted(months_data.keys())
    approved_data = [months_data[m]['approved'] for m in sorted_months]
    rejected_data = [months_data[m]['rejected'] for m in sorted_months]
    pending_data = [months_data[m]['pending'] for m in sorted_months]

    # Format month names for display
    month_names = []
    for m in sorted_months:
        try:
            date = datetime.strptime(m, '%Y-%m')
            month_names.append(date.strftime('%b %Y'))
        except ValueError:
            month_names.append(m)

    # Get recent leave requests
    recent_leaves = db.session.query(
        LeaveRequest, Employee
    ).join(Employee, LeaveRequest.employee_id == Employee.id)\
     .order_by(LeaveRequest.created_at.desc())\
     .limit(5).all()

    # Get statistics
    stats = {
        'employee_count': total_employees,
        'department_count': len(dept_names),
        'pending_leaves': LeaveRequest.query.filter_by(status='pending').count(),
        'approved_leaves': LeaveRequest.query.filter_by(status='approved').count(),
        'user_count': User.query.count(),
        'on_leave_count': status_dict.get('اجازة', 0),
        'absent_count': status_dict.get('غياب وهروب', 0),
        'officers_count': category_dict.get('ضباط', 0),
        'ncos_count': category_dict.get('ضباط صف', 0),
        'employees_count': category_dict.get('موظف', 0)
    }

    # Get employees with most used leave
    employees_with_most_leave = db.session.query(
        Employee.id,
        Employee.name,
        func.sum(LeaveRequest.total_days).label('total_days')
    ).join(LeaveRequest, Employee.id == LeaveRequest.employee_id)\
     .filter(LeaveRequest.status == 'approved')\
     .group_by(Employee.id)\
     .order_by(func.sum(LeaveRequest.total_days).desc())\
     .limit(5).all()

    return render_template('dashboard/index.html',
                          dept_names=dept_names,
                          dept_values=dept_values,
                          rank_names=rank_names,
                          rank_values=rank_values,
                          month_names=month_names,
                          approved_data=approved_data,
                          rejected_data=rejected_data,
                          pending_data=pending_data,
                          recent_leaves=recent_leaves,
                          stats=stats,
                          status_counts=status_counts,
                          category_counts=category_counts,
                          employees_with_most_leave=employees_with_most_leave)
