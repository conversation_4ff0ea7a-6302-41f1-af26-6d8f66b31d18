{% extends 'layouts/base.html' %}

{% block title %}طلب إجازة جديد - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.css">
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar-plus me-2"></i>طلب إجازة جديد
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('leaves.create') }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.employee_id.label(class="form-label") }}
                    {% if form.employee_id.errors %}
                        {{ form.employee_id(class="form-select is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.employee_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.employee_id(class="form-select") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.leave_type.label(class="form-label") }}
                    {% if form.leave_type.errors %}
                        {{ form.leave_type(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.leave_type.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.leave_type(class="form-control", placeholder="أدخل نوع الإجازة") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.initial_balance.label(class="form-label") }}
                    {% if form.initial_balance.errors %}
                        {{ form.initial_balance(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.initial_balance.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.initial_balance(class="form-control", placeholder="أدخل رصيد الإجازة", value="30") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    {{ form.start_date.label(class="form-label") }}
                    {% if form.start_date.errors %}
                        {{ form.start_date(class="form-control date-picker is-invalid", type="text") }}
                        <div class="invalid-feedback">
                            {% for error in form.start_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.start_date(class="form-control date-picker", placeholder="اختر تاريخ البداية", type="text") }}
                    {% endif %}
                </div>
                <div class="col-md-6">
                    {{ form.end_date.label(class="form-label") }}
                    {% if form.end_date.errors %}
                        {{ form.end_date(class="form-control date-picker is-invalid", type="text") }}
                        <div class="invalid-feedback">
                            {% for error in form.end_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.end_date(class="form-control date-picker", placeholder="اختر تاريخ النهاية", type="text") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label">مدة الإجازة الممنوحة</label>
                        <span id="leaveDurationBadge" class="badge bg-primary">0 يوم</span>
                    </div>
                    <div class="progress mb-2" style="height: 25px;">
                        <div id="daysProgress" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="30">0 / 30</div>
                    </div>
                    <div class="d-flex justify-content-between small text-muted">
                        <span>لا يتم حساب أيام العطل (الجمعة والسبت) من الإجازة الممنوحة</span>
                        <span id="workingDaysInfo">0</span>
                    </div>
                    {{ form.total_days() }}
                    {{ form.leave_balance() }}
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">رصيد الإجازة</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>الرصيد المدخل:</span>
                                <span id="initialBalanceDisplay">30 يوم</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>مدة الإجازة:</span>
                                <span id="leaveDuration">0 يوم</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الرصيد المتبقي:</span>
                                <span id="remainingBalance">30 يوم</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                {{ form.reason.label(class="form-label") }}
                {% if form.reason.errors %}
                    {{ form.reason(class="form-control is-invalid", rows=3) }}
                    <div class="invalid-feedback">
                        {% for error in form.reason.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.reason(class="form-control", rows=3, placeholder="أدخل سبب الإجازة") }}
                {% endif %}
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date pickers
        const startDatePicker = flatpickr("#start_date", {
            locale: "ar",
            dateFormat: "Y-m-d",
            allowInput: true,
            onChange: calculateDays
        });

        const endDatePicker = flatpickr("#end_date", {
            locale: "ar",
            dateFormat: "Y-m-d",
            allowInput: true,
            onChange: calculateDays
        });

        // Update initial balance display when input changes
        document.getElementById('initial_balance').addEventListener('input', function() {
            document.getElementById('initialBalanceDisplay').textContent = this.value + ' يوم';
            calculateDays();
        });

        // Calculate days between dates
        function calculateDays() {
            // Get values from date pickers (using flatpickr instances)
            let startDate = startDatePicker.selectedDates[0];
            let endDate = endDatePicker.selectedDates[0];
            const employeeId = document.getElementById('employee_id').value;
            const initialBalance = document.getElementById('initial_balance').value || 30;

            // If dates are selected directly in the input fields, try to parse them
            if (!startDate && document.getElementById('start_date').value) {
                startDate = new Date(document.getElementById('start_date').value);
            }

            if (!endDate && document.getElementById('end_date').value) {
                endDate = new Date(document.getElementById('end_date').value);
            }

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);

                // Check if end date is before start date
                if (end < start) {
                    // Silently set values to 0 without showing error message
                    document.getElementById('leaveDurationBadge').textContent = '0 يوم';
                    document.getElementById('leaveDurationBadge').className = 'badge bg-primary';
                    document.getElementById('total_days').value = 0;
                    document.getElementById('daysProgress').style.width = '0%';
                    document.getElementById('daysProgress').textContent = '0 / ' + initialBalance;
                    document.getElementById('workingDaysInfo').textContent = '0';
                    return;
                }

                // Calculate total days (including weekends)
                const diffTime = Math.abs(end - start);
                const totalDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

                // Calculate working days (excluding weekends) immediately
                let workingDaysCount = 0;
                let currentDate = new Date(start);
                while (currentDate <= end) {
                    // Skip weekends (Friday = 5, Saturday = 6 in JavaScript where Sunday = 0)
                    if (currentDate.getDay() !== 5 && currentDate.getDay() !== 6) {
                        workingDaysCount++;
                    }
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                // Display working days immediately
                document.getElementById('leaveDurationBadge').textContent = workingDaysCount + ' يوم';
                document.getElementById('leaveDurationBadge').className = 'badge bg-primary';

                // Set the total_days hidden field value
                document.getElementById('total_days').value = workingDaysCount;

                // Now call API to calculate working days (excluding weekends)
                fetch('/leaves/calculate-days', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        start_date: startDate,
                        end_date: endDate,
                        employee_id: employeeId
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('leaveDurationBadge').textContent = data.error;
                        document.getElementById('leaveDurationBadge').className = 'badge bg-danger';
                        document.getElementById('total_days').value = 0;
                        return;
                    }

                    // Update UI with calculated days
                    const workingDays = data.days;
                    const totalDays = data.days + data.weekends;
                    const initialBalance = parseInt(document.getElementById('initial_balance').value) || 30;

                    // Calculate remaining balance
                    const remainingBalance = parseInt(initialBalance) - workingDays;

                    // Set leave balance value (ensure it's not negative for display purposes)
                    document.getElementById('leave_balance').value = Math.max(0, remainingBalance);

                    // Update leave duration display
                    document.getElementById('leaveDurationBadge').textContent = workingDays + ' يوم';
                    document.getElementById('leaveDurationBadge').className = 'badge bg-primary';

                    // Update total_days value with the server-calculated working days
                    document.getElementById('total_days').value = workingDays;

                    // Update leave duration in the card
                    document.getElementById('leaveDuration').textContent = workingDays + ' يوم';

                    // Update working days info
                    const nonWorkingDays = totalDays - workingDays;
                    document.getElementById('workingDaysInfo').textContent = nonWorkingDays + ' يوم عطلة من ' + totalDays + ' يوم إجمالي';

                    // Update progress bar
                    const progressPercentage = Math.min(100, (workingDays / initialBalance) * 100);
                    document.getElementById('daysProgress').style.width = progressPercentage + '%';
                    document.getElementById('daysProgress').textContent = workingDays + ' / ' + initialBalance;

                    // Update remaining balance info
                    document.getElementById('remainingBalance').textContent = remainingBalance + ' يوم';

                    // Change progress bar color based on remaining balance
                    const progressBar = document.getElementById('daysProgress');

                    if (remainingBalance < 0) {
                        progressBar.className = 'progress-bar bg-danger';
                        document.getElementById('remainingBalance').innerHTML = '<span class="text-danger">' + remainingBalance + ' يوم (عجز)</span>';
                    } else if (remainingBalance < initialBalance * 0.3) {
                        progressBar.className = 'progress-bar bg-warning';
                    } else {
                        progressBar.className = 'progress-bar bg-success';
                    }
                })
                .catch(error => {
                    // Log error to console but don't show to user
                    console.error('Error calculating days:', error);

                    // Instead of showing error, just use the client-side calculation
                    // which we already did above
                });
            }
        }
    });
</script>
{% endblock %}
