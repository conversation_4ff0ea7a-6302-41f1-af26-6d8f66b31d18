{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-key"></i> تغيير كلمة المرور</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.change_password') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.current_password.label(class="form-label") }}
                            {% if form.current_password.errors %}
                                {{ form.current_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.current_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.current_password(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.new_password.label(class="form-label") }}
                            {% if form.new_password.errors %}
                                {{ form.new_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.new_password(class="form-control") }}
                            {% endif %}
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل.</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {% if form.confirm_password.errors %}
                                {{ form.confirm_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.confirm_password(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
