{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-shield"></i> إدارة المستخدمين</h2>
        <div>
            <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
            </a>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> قائمة المستخدمين</h5>
        </div>
        <div class="card-body">
            {% if users %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>نوع المستخدم</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر تحديث</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge bg-danger">مسؤول النظام</span>
                                {% else %}
                                <span class="badge bg-info">مستخدم عادي</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ user.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}" {% if user.id == current_user.id %}disabled{% endif %} data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ user.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف المستخدم <strong>{{ user.username }}</strong>؟
                                                {% if user.id == current_user.id %}
                                                <div class="alert alert-danger mt-3">
                                                    <i class="fas fa-exclamation-triangle"></i> لا يمكنك حذف حسابك الحالي.
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('auth.delete_user', id=user.id) }}" method="POST">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger" {% if user.id == current_user.id %}disabled{% endif %}>حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا يوجد مستخدمين مسجلين.</p>
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
