/* Main CSS for HRM System */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Card Styles */
.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
    border-bottom: none;
}

/* Table Styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Button Styles */
.btn {
    border-radius: 0.25rem;
    padding: 0.375rem 1rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Navbar Styles */
.navbar {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.navbar-brand {
    font-weight: 600;
}

.nav-link {
    font-weight: 500;
}

/* Footer Styles */
footer {
    border-top: 1px solid #dee2e6;
}

/* Dashboard Cards */
.card-counter {
    padding: 20px 10px;
    background-color: #fff;
    height: 100px;
    border-radius: 5px;
    transition: .3s linear all;
    margin-bottom: 20px;
}

.card-counter:hover {
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
    transition: .3s linear all;
}

.card-counter i {
    font-size: 5em;
    opacity: 0.2;
}

.card-counter .count-numbers {
    position: absolute;
    right: 35px;
    top: 20px;
    font-size: 32px;
    display: block;
}

.card-counter .count-name {
    position: absolute;
    right: 35px;
    top: 65px;
    font-style: italic;
    text-transform: capitalize;
    opacity: 0.5;
    display: block;
    font-size: 18px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body.dark-mode {
        background-color: #121212;
        color: #e0e0e0;
    }
    
    body.dark-mode .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    body.dark-mode .card-header {
        background-color: #2d2d2d !important;
        border-color: #333;
    }
    
    body.dark-mode .table {
        color: #e0e0e0;
    }
    
    body.dark-mode .table th {
        background-color: #2d2d2d;
    }
    
    body.dark-mode .table td {
        border-color: #333;
    }
    
    body.dark-mode .form-control,
    body.dark-mode .form-select {
        background-color: #2d2d2d;
        border-color: #444;
        color: #e0e0e0;
    }
    
    body.dark-mode .modal-content {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    body.dark-mode .border {
        border-color: #333 !important;
    }
    
    body.dark-mode .bg-light {
        background-color: #2d2d2d !important;
    }
}

/* RTL Specific Adjustments */
.dropdown-menu-end {
    left: 0;
    right: auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background-color: white !important;
    }
}
