<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض بيانات الموظف</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            font-weight: bold;
        }
        .employee-info {
            margin-bottom: 20px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        #scanButton {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        #qrScanner {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        #closeScanner {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        #scannerMessage {
            color: white;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>بيانات الموظف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="employeeData">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>امسح رمز QR لعرض بيانات الموظف
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scan Button -->
    <button id="scanButton" class="btn btn-primary">
        <i class="fas fa-qrcode"></i>
    </button>

    <!-- QR Scanner -->
    <div id="qrScanner">
        <div id="closeScanner">
            <i class="fas fa-times"></i>
        </div>
        <video id="preview"></video>
        <div id="scannerMessage">جاري البحث عن رمز QR...</div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://rawgit.com/schmich/instascan-builds/master/instascan.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const scanButton = document.getElementById('scanButton');
            const qrScanner = document.getElementById('qrScanner');
            const closeScanner = document.getElementById('closeScanner');
            const employeeData = document.getElementById('employeeData');
            const preview = document.getElementById('preview');
            let scanner = null;

            // Function to display employee data
            function displayEmployeeData(data) {
                try {
                    const employee = JSON.parse(data);
                    
                    // Create HTML for employee data
                    let html = `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <h4>${employee['الاسم'] || ''}</h4>
                                <span class="badge bg-primary">${employee['الرتبة'] || ''}</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="employee-info">
                                    <div class="info-label">الرقم العسكري</div>
                                    <div>${employee['الرقم العسكري'] || 'غير متوفر'}</div>
                                </div>
                                <div class="employee-info">
                                    <div class="info-label">الوحدة</div>
                                    <div>${employee['الوحدة'] || 'غير متوفر'}</div>
                                </div>
                                <div class="employee-info">
                                    <div class="info-label">العمل المكلف به</div>
                                    <div>${employee['العمل المكلف به'] || 'غير متوفر'}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="employee-info">
                                    <div class="info-label">الفئة</div>
                                    <div>${employee['الفئة'] || 'غير متوفر'}</div>
                                </div>
                                <div class="employee-info">
                                    <div class="info-label">الرقم الوطني</div>
                                    <div>${employee['الرقم الوطني'] || 'غير متوفر'}</div>
                                </div>
                                <div class="employee-info">
                                    <div class="info-label">الحالة</div>
                                    <div>
                    `;
                    
                    // Add status badge
                    const status = employee['الحالة'] || '';
                    if (status === 'مستمر') {
                        html += `<span class="badge bg-success">مستمر</span>`;
                    } else if (status === 'غياب وهروب') {
                        html += `<span class="badge bg-danger">غياب وهروب</span>`;
                    } else if (status === 'مكلف') {
                        html += `<span class="badge bg-primary">مكلف</span>`;
                    } else if (status === 'اجازة') {
                        html += `<span class="badge bg-info">اجازة</span>`;
                    } else if (status === 'ايقاف عن العمل') {
                        html += `<span class="badge bg-warning">ايقاف عن العمل</span>`;
                    } else if (status === 'متفرق') {
                        html += `<span class="badge bg-secondary">متفرق</span>`;
                    } else if (status === 'عيادة طبية') {
                        html += `<span class="badge bg-info">عيادة طبية</span>`;
                    } else {
                        html += `<span class="badge bg-secondary">غير محدد</span>`;
                    }
                    
                    html += `
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>تم مسح رمز QR بنجاح
                        </div>
                    `;
                    
                    employeeData.innerHTML = html;
                } catch (error) {
                    employeeData.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>حدث خطأ في قراءة البيانات
                        </div>
                    `;
                    console.error('Error parsing QR data:', error);
                }
            }

            // Initialize scanner when button is clicked
            scanButton.addEventListener('click', function() {
                qrScanner.style.display = 'flex';
                
                // Initialize scanner if not already done
                if (!scanner) {
                    scanner = new Instascan.Scanner({ 
                        video: preview,
                        mirror: false
                    });
                    
                    scanner.addListener('scan', function(content) {
                        // Close scanner
                        qrScanner.style.display = 'none';
                        scanner.stop();
                        
                        // Display employee data
                        displayEmployeeData(content);
                    });
                    
                    // Start scanner with available camera
                    Instascan.Camera.getCameras().then(function(cameras) {
                        if (cameras.length > 0) {
                            // Try to use the back camera if available
                            const backCamera = cameras.find(camera => camera.name.toLowerCase().includes('back'));
                            scanner.start(backCamera || cameras[0]);
                        } else {
                            document.getElementById('scannerMessage').textContent = 'لم يتم العثور على كاميرا';
                        }
                    }).catch(function(e) {
                        console.error(e);
                        document.getElementById('scannerMessage').textContent = 'حدث خطأ في الوصول إلى الكاميرا';
                    });
                } else {
                    scanner.start();
                }
            });

            // Close scanner
            closeScanner.addEventListener('click', function() {
                qrScanner.style.display = 'none';
                if (scanner) {
                    scanner.stop();
                }
            });

            // Handle URL parameters if any (for testing)
            const urlParams = new URLSearchParams(window.location.search);
            const testData = urlParams.get('data');
            if (testData) {
                try {
                    const decodedData = decodeURIComponent(testData);
                    displayEmployeeData(decodedData);
                } catch (e) {
                    console.error('Error decoding test data:', e);
                }
            }
        });
    </script>
</body>
</html>
