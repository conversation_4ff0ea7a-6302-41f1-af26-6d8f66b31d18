{% extends 'layouts/auth_base.html' %}

{% block content %}
<div class="text-center mb-4">
    <img src="{{ url_for('static', filename='img/hrm_logo.svg') }}" alt="Logo" width="180" class="mb-3">
    <h2 class="mb-3">تسجيل الدخول</h2>
    <p class="text-muted">يرجى إدخال بيانات الدخول للوصول إلى نظام إدارة الموارد البشرية</p>
</div>

<form method="POST" action="{{ url_for('auth.login') }}">
    {{ form.hidden_tag() }}

    <div class="mb-3">
        {{ form.username.label(class="form-label") }}
        {% if form.username.errors %}
            {{ form.username(class="form-control is-invalid") }}
            <div class="invalid-feedback">
                {% for error in form.username.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% else %}
            {{ form.username(class="form-control", placeholder="أدخل اسم المستخدم") }}
        {% endif %}
    </div>

    <div class="mb-3">
        {{ form.password.label(class="form-label") }}
        {% if form.password.errors %}
            {{ form.password(class="form-control is-invalid") }}
            <div class="invalid-feedback">
                {% for error in form.password.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% else %}
            {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
        {% endif %}
    </div>

    <div class="mb-3 form-check">
        {{ form.remember_me(class="form-check-input") }}
        {{ form.remember_me.label(class="form-check-label") }}
    </div>

    <div class="d-grid gap-2">
        {{ form.submit(class="btn btn-primary btn-lg") }}
    </div>
</form>
{% endblock %}
