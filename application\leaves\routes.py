from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from .. import db
from ..models import LeaveRequest, LeaveType, Employee, User, AuditLog, Notification
from .forms import LeaveRequestForm, LeaveA<PERSON>rovalForm, LeaveSearchForm
from datetime import datetime, timezone, timedelta
import json
import calendar

leaves_bp = Blueprint('leaves', __name__)

# Define official holidays
OFFICIAL_HOLIDAYS = [
    '2023-01-01',  # New Year's Day
    '2023-01-07',  # Coptic Christmas
    '2023-01-25',  # Revolution Day
    '2023-04-16',  # Coptic Easter
    '2023-04-25',  # Sinai Liberation Day
    '2023-05-01',  # Labor Day
    '2023-06-30',  # Revolution Day
    '2023-07-23',  # Revolution Day
    '2023-10-06',  # Armed Forces Day
    '2023-12-25',  # Christmas Day
    # Add Eid holidays and other Islamic holidays (these change each year)
    '2023-04-21',  # <PERSON>id al-Fitr
    '2023-04-22',  # Eid al-Fitr
    '2023-04-23',  # Eid al-Fitr
    '2023-06-28',  # Eid al-<PERSON>ha
    '2023-06-29',  # Eid al-<PERSON>
    '2023-06-30',  # Eid al-Adha
    '2023-07-01',  # Eid al-Adha
    '2023-07-19',  # Islamic New Year
    '2023-09-27',  # Prophet's Birthday
    # 2024 holidays
    '2024-01-01',  # New Year's Day
    '2024-01-07',  # Coptic Christmas
    '2024-01-25',  # Revolution Day
    '2024-04-01',  # Sinai Liberation Day
    '2024-05-01',  # Labor Day
    '2024-06-30',  # Revolution Day
    '2024-07-23',  # Revolution Day
    '2024-10-06',  # Armed Forces Day
    '2024-12-25',  # Christmas Day
    # Add Eid holidays and other Islamic holidays for 2024
    '2024-04-10',  # Eid al-Fitr
    '2024-04-11',  # Eid al-Fitr
    '2024-04-12',  # Eid al-Fitr
    '2024-06-17',  # Eid al-Adha
    '2024-06-18',  # Eid al-Adha
    '2024-06-19',  # Eid al-Adha
    '2024-06-20',  # Eid al-Adha
    '2024-07-08',  # Islamic New Year
    '2024-09-16',  # Prophet's Birthday
]

@leaves_bp.route('/')
@login_required
def index():
    # Create search form
    form = LeaveSearchForm()

    # Populate employee dropdown
    form.employee.choices = [(0, 'جميع الموظفين')] + [
        (e.id, e.name) for e in Employee.query.order_by(Employee.name).all()
    ]

    # Populate leave type dropdown
    form.leave_type.choices = [(0, 'جميع أنواع الإجازات')] + [
        (lt.id, lt.name) for lt in LeaveType.query.order_by(LeaveType.name).all()
    ]

    # Process search parameters
    employee_id = request.args.get('employee', type=int)
    leave_type_id = request.args.get('leave_type', type=int)
    status_id = request.args.get('status', type=int)
    date_from = request.args.get('date_from', type=str)
    date_to = request.args.get('date_to', type=str)

    # Base query
    query = LeaveRequest.query

    # Apply filters if provided
    if employee_id and employee_id > 0:
        query = query.filter(LeaveRequest.employee_id == employee_id)

    if leave_type_id and leave_type_id > 0:
        query = query.filter(LeaveRequest.leave_type_id == leave_type_id)

    if status_id:
        if status_id == 1:
            query = query.filter(LeaveRequest.status == 'pending')
        elif status_id == 2:
            query = query.filter(LeaveRequest.status == 'approved')
        elif status_id == 3:
            query = query.filter(LeaveRequest.status == 'rejected')

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(LeaveRequest.start_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(LeaveRequest.end_date <= date_to_obj)
        except ValueError:
            pass

    # Get paginated results
    page = request.args.get('page', 1, type=int)
    leave_requests = query.order_by(LeaveRequest.created_at.desc()).paginate(page=page, per_page=10)

    return render_template('leaves/index.html',
                          leave_requests=leave_requests,
                          form=form,
                          employee_id=employee_id,
                          leave_type_id=leave_type_id,
                          status_id=status_id,
                          date_from=date_from,
                          date_to=date_to)

@leaves_bp.route('/calendar')
@login_required
def calendar_view():
    # Get all leave requests
    leave_requests = LeaveRequest.query.all()

    # Format events for FullCalendar
    events = []
    for leave in leave_requests:
        # Get employee name
        employee = Employee.query.get(leave.employee_id)
        employee_name = employee.name if employee else 'غير معروف'

        # Get leave type name
        leave_type = LeaveType.query.get(leave.leave_type_id)
        leave_type_name = leave_type.name if leave_type else 'غير معروف'

        # Determine event color based on status
        event_class = 'fc-event-approved'
        if leave.status == 'pending':
            event_class = 'fc-event-pending'
        elif leave.status == 'rejected':
            event_class = 'fc-event-rejected'

        # Create event
        event = {
            'id': leave.id,
            'title': f'{employee_name} - {leave_type_name}',
            'start': leave.start_date.strftime('%Y-%m-%d'),
            'end': (leave.end_date + timedelta(days=1)).strftime('%Y-%m-%d'),  # Add 1 day to make it inclusive
            'className': event_class,
            'employee_name': employee_name,
            'leave_type': leave_type_name,
            'days_count': leave.total_days,
            'reason': leave.reason,
            'status': leave.status
        }

        events.append(event)

    return render_template('leaves/calendar.html', events=events, holidays=OFFICIAL_HOLIDAYS)

@leaves_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = LeaveRequestForm()

    # Populate employee dropdown
    form.employee_id.choices = [
        (e.id, e.name) for e in Employee.query.order_by(Employee.name).all()
    ]

    if form.validate_on_submit():
        # Calculate total days
        delta = form.end_date.data - form.start_date.data
        total_days = delta.days + 1  # Include both start and end dates

        # Get or create leave type
        leave_type = LeaveType.query.filter_by(name=form.leave_type.data).first()
        if not leave_type:
            leave_type = LeaveType(
                name=form.leave_type.data,
                description=f"نوع إجازة: {form.leave_type.data}",
                max_days_per_year=30,
                requires_approval=True
            )
            db.session.add(leave_type)
            db.session.flush()  # Get the ID without committing

        # Create new leave request
        leave_request = LeaveRequest(
            employee_id=form.employee_id.data,
            leave_type_id=leave_type.id,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            total_days=int(form.total_days.data or 0),
            reason=form.reason.data,
            status='pending'
        )

        # Store initial balance and remaining balance in the reason field for reference
        initial_balance = form.initial_balance.data or '0'
        remaining_balance = form.leave_balance.data or '0'
        leave_request.reason = f"{form.reason.data or ''} | رصيد الإجازة: {initial_balance} | المتبقي بعد الإجازة: {remaining_balance}"

        db.session.add(leave_request)

        # Create notification for admins
        admin_users = User.query.filter_by(role='admin').all()
        employee = Employee.query.get(form.employee_id.data)

        for admin in admin_users:
            notification = Notification(
                user_id=admin.id,
                title='طلب إجازة جديد',
                message=f'تم تقديم طلب إجازة جديد من {employee.name} من نوع {form.leave_type.data}',
                type='leave_request',
                link=url_for('leaves.approve', id=leave_request.id)
            )
            db.session.add(notification)

        # Log the action
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='leave_request',
            entity_id=leave_request.id,
            details=f"تم إنشاء طلب إجازة جديد للموظف: {employee.name} من نوع: {form.leave_type.data}",
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تقديم طلب الإجازة بنجاح', 'success')
        return redirect(url_for('leaves.index'))

    return render_template('leaves/create.html', form=form)

@leaves_bp.route('/<int:id>/view')
@login_required
def view(id):
    leave_request = LeaveRequest.query.get_or_404(id)
    employee = Employee.query.get(leave_request.employee_id)
    leave_type = LeaveType.query.get(leave_request.leave_type_id)

    # Get approver name if approved
    approver_name = None
    if leave_request.approved_by:
        approver = User.query.get(leave_request.approved_by)
        if approver:
            approver_name = approver.email

    return render_template('leaves/view.html',
                          leave_request=leave_request,
                          employee=employee,
                          leave_type=leave_type,
                          approver_name=approver_name)

@leaves_bp.route('/<int:id>/approve', methods=['GET', 'POST'])
@login_required
def approve(id):
    # Check if user has admin role
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('leaves.index'))

    leave_request = LeaveRequest.query.get_or_404(id)
    employee = Employee.query.get(leave_request.employee_id)
    leave_type = LeaveType.query.get(leave_request.leave_type_id)

    form = LeaveApprovalForm(obj=leave_request)

    if form.validate_on_submit():
        leave_request.status = form.status.data
        leave_request.comments = form.comments.data
        leave_request.approved_by = current_user.id
        leave_request.approved_at = datetime.now(timezone.utc)

        # Log the action
        status_text = {
            'approved': 'الموافقة على',
            'rejected': 'رفض',
            'pending': 'إعادة لحالة الانتظار'
        }

        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='leave_request',
            entity_id=leave_request.id,
            details=f"تم {status_text[form.status.data]} طلب إجازة للموظف: {employee.name}",
            ip_address=request.remote_addr
        )
        db.session.add(log)

        # Create notification for the employee
        if employee.user_id:
            notification_type = 'leave_approved' if form.status.data == 'approved' else 'leave_rejected'
            notification_title = 'تمت الموافقة على طلب الإجازة' if form.status.data == 'approved' else 'تم رفض طلب الإجازة'
            notification_message = f'تمت الموافقة على طلب إجازتك من {leave_request.start_date.strftime("%Y-%m-%d")} إلى {leave_request.end_date.strftime("%Y-%m-%d")}' if form.status.data == 'approved' else f'تم رفض طلب إجازتك من {leave_request.start_date.strftime("%Y-%m-%d")} إلى {leave_request.end_date.strftime("%Y-%m-%d")}'

            notification = Notification(
                user_id=employee.user_id,
                title=notification_title,
                message=notification_message,
                type=notification_type,
                link=url_for('leaves.view', id=leave_request.id)
            )
            db.session.add(notification)

        db.session.commit()
        flash(f'تم {status_text[form.status.data]} طلب الإجازة بنجاح', 'success')
        return redirect(url_for('leaves.index'))

    return render_template('leaves/approve.html',
                          form=form,
                          leave_request=leave_request,
                          employee=employee,
                          leave_type=leave_type)

@leaves_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    # Check if user has admin role
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('leaves.index'))

    leave_request = LeaveRequest.query.get_or_404(id)
    employee = Employee.query.get(leave_request.employee_id)

    # Log the action before deletion
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='leave_request',
        entity_id=leave_request.id,
        details=f"تم حذف طلب إجازة للموظف: {employee.name}",
        ip_address=request.remote_addr
    )
    db.session.add(log)

    # Delete the leave request
    db.session.delete(leave_request)
    db.session.commit()

    flash('تم حذف طلب الإجازة بنجاح', 'success')
    return redirect(url_for('leaves.index'))

# Helper function to calculate working days between two dates
def calculate_days(start_date, end_date):
    # Calculate number of days between two dates (inclusive), excluding weekends and holidays
    days_count = 0
    weekend_days = 0
    holiday_days = 0
    current_date = start_date

    while current_date <= end_date:
        # Count weekends (Friday = 4, Saturday = 5 in Python's datetime where Monday = 0)
        if current_date.weekday() in [4, 5]:  # Friday or Saturday
            weekend_days += 1
        # Count official holidays (that are not on weekends)
        elif current_date.strftime('%Y-%m-%d') in OFFICIAL_HOLIDAYS:
            holiday_days += 1
        else:
            # Count working days
            days_count += 1

        current_date += timedelta(days=1)

    return days_count, weekend_days, holiday_days

# API endpoint to calculate leave days
@leaves_bp.route('/calculate-days', methods=['POST'])
@login_required
def calculate_leave_days():
    data = request.get_json()

    try:
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()

        # Ensure start date is before or equal to end date
        if start_date > end_date:
            return jsonify({'error': 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'}), 400

        # Calculate total days (including weekends and holidays)
        total_days = (end_date - start_date).days + 1

        # Calculate working days, weekend days, and holiday days
        working_days, weekend_days, holiday_days = calculate_days(start_date, end_date)

        # Calculate non-working days (weekends + holidays)
        non_working_days = weekend_days + holiday_days

        # No need to calculate leave balance from database as it will be provided by the user
        # We'll just return the calculated days

        return jsonify({
            'days': working_days,
            'weekends': weekend_days,
            'holidays': holiday_days,
            'non_working_days': non_working_days,
            'total_days': total_days
        })

    except ValueError as e:
        return jsonify({'error': str(e)}), 400