import sqlite3
import os

def fix_specific_records(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_log'")
        if cursor.fetchone():
            print(f"جدول audit_log موجود في {db_path}")
            
            # الحصول على معرف المستخدم الأول
            cursor.execute("SELECT id FROM user LIMIT 1")
            user_id = cursor.fetchone()
            
            if user_id:
                user_id = user_id[0]
                print(f"تم العثور على معرف المستخدم: {user_id}")
                
                # السجلات المحددة التي تحتاج إلى إصلاح
                record_ids = [414, 415, 418, 420]
                
                # عرض السجلات المحددة
                for record_id in record_ids:
                    cursor.execute("SELECT * FROM audit_log WHERE id = ?", (record_id,))
                    record = cursor.fetchone()
                    if record:
                        print(f"السجل {record_id}: {record}")
                    else:
                        print(f"لم يتم العثور على السجل {record_id}")
                
                # تحديث السجلات المحددة
                for record_id in record_ids:
                    cursor.execute("UPDATE audit_log SET user_id = ? WHERE id = ?", (user_id, record_id))
                    print(f"تم تحديث السجل {record_id} بمعرف المستخدم {user_id}")
                
                conn.commit()
                print(f"تم تحديث {len(record_ids)} سجل بنجاح")
                
                # التحقق من نجاح التحديث
                for record_id in record_ids:
                    cursor.execute("SELECT user_id FROM audit_log WHERE id = ?", (record_id,))
                    updated_user_id = cursor.fetchone()
                    if updated_user_id:
                        print(f"السجل {record_id} الآن له معرف المستخدم: {updated_user_id[0]}")
                    else:
                        print(f"لم يتم العثور على السجل {record_id} بعد التحديث")
            else:
                print("لم يتم العثور على أي مستخدم في النظام")
        else:
            print(f"جدول audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في إصلاح السجلات المحددة في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        fix_specific_records(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من إصلاح السجلات المحددة")
