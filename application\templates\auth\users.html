{% extends 'layouts/base.html' %}

{% block title %}إدارة المستخدمين - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>إدارة المستخدمين
        </h5>
        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-sm">
            <i class="fas fa-user-plus me-1"></i>إضافة مستخدم جديد
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>آخر تسجيل دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger">مسؤول</span>
                            {% elif user.role == 'manager' %}
                                <span class="badge bg-warning">مدير</span>
                            {% else %}
                                <span class="badge bg-info">موظف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل الدخول بعد' }}</td>
                        <td>
                            {% if user.id != current_user.id %}
                            <form action="{{ url_for('auth.toggle_user', id=user.id) }}" method="POST" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm {% if user.is_active %}btn-warning{% else %}btn-success{% endif %}" title="{{ 'تعطيل' if user.is_active else 'تفعيل' }}">
                                    <i class="fas {% if user.is_active %}fa-user-slash{% else %}fa-user-check{% endif %}"></i>
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
