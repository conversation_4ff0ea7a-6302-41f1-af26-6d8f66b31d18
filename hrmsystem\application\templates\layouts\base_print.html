<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} | نظام إدارة الموارد البشرية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Print CSS -->
    <style>
        @page {
            size: A4 portrait;
            margin: 1.5cm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #000;
            background-color: #fff;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-subtitle {
            font-size: 16px;
            margin-bottom: 5px;
            background-color: #ddd;
            padding: 5px;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .report-table th, .report-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        
        .report-table th {
            background-color: #b8d1f3;
            font-weight: bold;
        }
        
        .report-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .page-number {
            text-align: center;
            margin-top: 20px;
        }
        
        /* تخصيص الألوان حسب نوع التقرير */
        .report-rank th {
            background-color: #b8d1f3; /* أزرق فاتح */
        }
        
        .report-unit th {
            background-color: #c6e0b4; /* أخضر فاتح */
        }
        
        .report-category th {
            background-color: #f8cbad; /* برتقالي فاتح */
        }
        
        .report-status th {
            background-color: #ffe699; /* أصفر فاتح */
        }
        
        .report-leave th {
            background-color: #d9d2e9; /* بنفسجي فاتح */
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            a {
                text-decoration: none;
                color: #000;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
    
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container">
        {% block content %}{% endblock %}
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Print Script -->
    <script>
        $(document).ready(function() {
            // إذا كان هناك زر طباعة، قم بإضافة وظيفة الطباعة له
            $('.btn-print').click(function() {
                window.print();
                return false;
            });
            
            // إذا كان هناك زر رجوع، قم بإضافة وظيفة الرجوع له
            $('.btn-back').click(function() {
                window.history.back();
                return false;
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
