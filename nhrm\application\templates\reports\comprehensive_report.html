{% extends 'layouts/base.html' %}

{% block title %}التقرير الشامل{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        margin-bottom: 20px;
    }

    .stat-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
    }

    .stat-label {
        font-size: 1rem;
        opacity: 0.8;
    }

    .status-badge {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .container {
            width: 100%;
            max-width: 100%;
        }

        .card {
            break-inside: avoid;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-3 no-print">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">التقرير الشامل</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center">
                <h3><i class="fas fa-chart-line me-2"></i>التقرير الشامل</h3>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>طباعة التقرير
                    </button>
                    <a href="{{ url_for('reports.export_report', report_type='employees') }}" class="btn btn-outline-success">
                        <i class="fas fa-file-excel me-1"></i>تصدير إلى Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ employees|length }}</div>
                            <div class="stat-label">إجمالي الموظفين</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ status_counts.get('مستمر', 0) }}</div>
                            <div class="stat-label">الموظفين المستمرين</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-warning text-dark h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ status_counts.get('اجازة', 0) }}</div>
                            <div class="stat-label">الموظفين في إجازة</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ status_counts.get('غياب وهروب', 0) }}</div>
                            <div class="stat-label">الغياب والهروب</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-user-slash"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">توزيع الموظفين حسب الرتبة</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="rankChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">توزيع الموظفين حسب الوحدة</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="unitChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">توزيع الموظفين حسب الحالة</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">توزيع الموظفين حسب الفئة</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل الموظفين حسب الحالة</h5>
                    <div class="no-print">
                        <button class="btn btn-sm btn-outline-primary" id="toggleDetailsBtn">
                            <i class="fas fa-eye me-1"></i>عرض/إخفاء التفاصيل
                        </button>
                    </div>
                </div>
                <div class="card-body p-0" id="detailsContainer" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>الحالة</th>
                                    <th>الاسم</th>
                                    <th>الرقم العسكري</th>
                                    <th>الرتبة</th>
                                    <th>الوحدة</th>
                                    <th>العمل المكلف به</th>
                                    <th>تاريخ التعيين</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>
                                        {% if employee.employee_status == 'مستمر' %}
                                            <span class="badge bg-success">مستمر</span>
                                        {% elif employee.employee_status == 'غياب وهروب' %}
                                            <span class="badge bg-danger">غياب وهروب</span>
                                        {% elif employee.employee_status == 'مكلف' %}
                                            <span class="badge bg-primary">مكلف</span>
                                        {% elif employee.employee_status == 'اجازة' %}
                                            <span class="badge bg-info">اجازة</span>
                                        {% elif employee.employee_status == 'ايقاف عن العمل' %}
                                            <span class="badge bg-warning text-dark">ايقاف عن العمل</span>
                                        {% elif employee.employee_status == 'متفرق' %}
                                            <span class="badge bg-secondary">متفرق</span>
                                        {% elif employee.employee_status == 'عيادة طبية' %}
                                            <span class="badge bg-info text-dark">عيادة طبية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ employee.name }}</td>
                                    <td>{{ employee.military_id }}</td>
                                    <td>{{ employee.military_rank }}</td>
                                    <td>{{ employee.department_name }}</td>
                                    <td>{{ employee.position }}</td>
                                    <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير متوفر' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Footer -->
    <div class="row mt-5">
        <div class="col-md-12 text-center">
            <p class="text-muted">
                تم إنشاء هذا التقرير في {{ current_time.strftime('%Y-%m-%d %H:%M') }}
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle details
        const toggleDetailsBtn = document.getElementById('toggleDetailsBtn');
        const detailsContainer = document.getElementById('detailsContainer');

        toggleDetailsBtn.addEventListener('click', function() {
            if (detailsContainer.style.display === 'none') {
                detailsContainer.style.display = 'block';
                toggleDetailsBtn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>إخفاء التفاصيل';
            } else {
                detailsContainer.style.display = 'none';
                toggleDetailsBtn.innerHTML = '<i class="fas fa-eye me-1"></i>عرض التفاصيل';
            }
        });

        // Rank Chart
        const rankCtx = document.getElementById('rankChart').getContext('2d');
        const rankData = {
            labels: [],
            datasets: [{
                label: 'عدد الموظفين',
                data: [],
                backgroundColor: [],
                borderColor: [],
                borderWidth: 1
            }]
        };

        {% for rank, count in rank_counts.items() %}
            rankData.labels.push('{{ rank }}');
            rankData.datasets[0].data.push({{ count }});
            // Generate a color based on the index
            const hue{{ loop.index }} = ({{ loop.index0 }} * 137.5) % 360;
            rankData.datasets[0].backgroundColor.push(`hsl(${hue{{ loop.index }}}, 70%, 60%)`);
            rankData.datasets[0].borderColor.push(`hsl(${hue{{ loop.index }}}, 70%, 50%)`);
        {% endfor %}

        new Chart(rankCtx, {
            type: 'bar',
            data: rankData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Unit Chart
        const unitCtx = document.getElementById('unitChart').getContext('2d');
        const unitData = {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [],
                borderColor: [],
                borderWidth: 1
            }]
        };

        {% for unit, count in unit_counts.items() %}
            unitData.labels.push('{{ unit }}');
            unitData.datasets[0].data.push({{ count }});
            // Generate a color based on the index
            const unitHue{{ loop.index }} = ({{ loop.index0 }} * 137.5) % 360;
            unitData.datasets[0].backgroundColor.push(`hsl(${unitHue{{ loop.index }}}, 70%, 60%)`);
            unitData.datasets[0].borderColor.push(`hsl(${unitHue{{ loop.index }}}, 70%, 50%)`);
        {% endfor %}

        new Chart(unitCtx, {
            type: 'pie',
            data: unitData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusData = {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [],
                borderColor: [],
                borderWidth: 1
            }]
        };

        const statusColors = {
            'مستمر': '#28a745',
            'غياب وهروب': '#dc3545',
            'مكلف': '#007bff',
            'اجازة': '#17a2b8',
            'ايقاف عن العمل': '#ffc107',
            'متفرق': '#6c757d',
            'عيادة طبية': '#20c997'
        };

        {% for status, count in status_counts.items() %}
            statusData.labels.push('{{ status }}');
            statusData.datasets[0].data.push({{ count }});
            statusData.datasets[0].backgroundColor.push(statusColors['{{ status }}'] || '#6c757d');
            statusData.datasets[0].borderColor.push(statusColors['{{ status }}'] ? statusColors['{{ status }}'].replace(')', ', 0.8)') : '#5a6268');
        {% endfor %}

        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryData = {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [],
                borderColor: [],
                borderWidth: 1
            }]
        };

        const categoryColors = {
            'ضباط': '#007bff',
            'ضباط صف': '#28a745',
            'موظف': '#fd7e14'
        };

        {% for category, count in category_counts.items() %}
            categoryData.labels.push('{{ category }}');
            categoryData.datasets[0].data.push({{ count }});
            categoryData.datasets[0].backgroundColor.push((categoryColors['{{ category }}'] || '#6c757d') + '99');
            categoryData.datasets[0].borderColor.push(categoryColors['{{ category }}'] || '#6c757d');
        {% endfor %}

        new Chart(categoryCtx, {
            type: 'polarArea',
            data: categoryData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
