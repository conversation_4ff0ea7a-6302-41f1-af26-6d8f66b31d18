{% extends 'layouts/base.html' %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css">
<style>
    .fc-event {
        cursor: pointer;
    }
    .fc-toolbar-title {
        font-size: 1.5rem !important;
    }
    .fc-button {
        background-color: #0d6efd !important;
        border-color: #0d6efd !important;
    }
    .fc-button-active {
        background-color: #0a58ca !important;
        border-color: #0a58ca !important;
    }
    .fc-day-today {
        background-color: rgba(13, 110, 253, 0.1) !important;
    }
    .fc-daygrid-day-number, .fc-col-header-cell-cushion {
        text-decoration: none !important;
        color: #212529 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar"></i> تقويم الإجازات</h2>
        <div>
            <a href="{{ url_for('leaves.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة طلب إجازة جديد
            </a>
            <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى الإجازات
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> تقويم الإجازات</h5>
        </div>
        <div class="card-body">
            <div id="calendar"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/locales/ar.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var calendarEl = document.getElementById('calendar');
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                day: 'يوم',
                list: 'قائمة'
            },
            events: {{ events|tojson }},
            eventClick: function(info) {
                window.location.href = info.event.url;
            },
            eventTimeFormat: {
                hour: '2-digit',
                minute: '2-digit',
                meridiem: false,
                hour12: false
            },
            firstDay: 0, // Sunday
            weekends: true,
            weekNumbers: false,
            navLinks: true,
            dayMaxEvents: true,
            eventDisplay: 'block',
            displayEventTime: false
        });
        calendar.render();
    });
</script>
{% endblock %}
