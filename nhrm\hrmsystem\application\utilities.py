import os
import secrets
from PIL import Image
from flask import current_app, url_for
from datetime import datetime, timedelta
from .models import Holiday
from . import db
import qrcode
from qrcode import constants
import io
import base64
import calendar

def save_picture(form_picture, folder='profile_pics', size=(150, 150)):
    """Save a picture to the filesystem and return the filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(current_app.root_path, 'static/img', folder, picture_fn)

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)

    # Resize image
    i = Image.open(form_picture)
    i.thumbnail(size)
    i.save(picture_path)

    return picture_fn

def calculate_leave_days(start_date, end_date, exclude_weekends=True, exclude_holidays=True):
    """Calculate the number of days between two dates, excluding weekends and holidays if specified"""
    if start_date > end_date:
        return 0

    # Get all dates between start_date and end_date
    delta = end_date - start_date
    all_days = [start_date + timedelta(days=i) for i in range(delta.days + 1)]

    # Get weekend days from config
    weekend_days = current_app.config.get('WEEKEND_DAYS', [4, 5])  # Default: Friday and Saturday

    # Filter out weekend days if needed
    if exclude_weekends:
        all_days = [day for day in all_days if day.weekday() not in weekend_days]

    # Filter out holidays if needed
    if exclude_holidays:
        try:
            holidays = Holiday.query.filter(
                Holiday.date.between(start_date, end_date)
            ).all()
            holiday_dates = [holiday.date for holiday in holidays]
            all_days = [day for day in all_days if day not in holiday_dates]
        except Exception:
            # If there's an error (e.g., Holiday table doesn't exist yet), just continue
            pass

    return len(all_days)

def generate_qr_code(data, size=200):
    """Generate a QR code for the given data"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white").convert("RGB")

    # Resize the image to the requested size
    img = img.resize((size, size))

    # Convert to base64 for embedding in HTML
    buffered = io.BytesIO()
    img.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str

def generate_employee_qr_code(employee, size=200):
    """Generate a QR code with employee data"""
    try:
        # Create a string with employee data
        employee_data = f"الاسم: {employee.name}\n"
        employee_data += f"الرقم العسكري: {employee.military_id}\n"

        if employee.national_id:
            employee_data += f"الرقم الوطني: {employee.national_id}\n"

        if employee.military_rank:
            employee_data += f"الرتبة: {employee.military_rank}\n"

        if employee.unit:
            employee_data += f"الوحدة: {employee.unit}\n"

        if employee.position:
            employee_data += f"العمل المكلف به: {employee.position}\n"

        if hasattr(employee, 'category') and employee.category:
            try:
                employee_data += f"الفئة: {employee.category.value}\n"
            except:
                employee_data += f"الفئة: {employee.category}\n"

        if hasattr(employee, 'status') and employee.status:
            try:
                employee_data += f"الحالة: {employee.status.value}\n"
            except:
                employee_data += f"الحالة: {employee.status}\n"

        if employee.phone:
            employee_data += f"رقم الهاتف: {employee.phone}\n"

        print(f"QR Code data: {employee_data}")

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=constants.ERROR_CORRECT_H,  # Higher error correction for more data
            box_size=10,
            border=4,
        )
        qr.add_data(employee_data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Resize the image to the requested size
        img = img.resize((size, size))

        # Convert to base64 for embedding in HTML
        buffered = io.BytesIO()
        img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"
    except Exception as e:
        print(f"Error generating QR code: {e}")
        # Return a static QR code image URL as fallback
        return url_for('static', filename='img/default_qr.png')

def format_date(date, format='%Y-%m-%d'):
    """Format a date object to string"""
    if date:
        return date.strftime(format)
    return ""

def parse_date(date_str, format='%Y-%m-%d'):
    """Parse a date string to date object"""
    if date_str:
        try:
            return datetime.strptime(date_str, format).date()
        except ValueError:
            return None
    return None

def get_employee_status_color(status):
    """Get color for employee status"""
    colors = {
        'مستمر': 'success',
        'غائب/هارب': 'danger',
        'منتدب': 'info',
        'في إجازة': 'warning',
        'موقوف': 'secondary',
        'متفرق': 'primary',
        'عيادة طبية': 'dark'
    }
    return colors.get(status.value, 'secondary')

def get_leave_status_color(status):
    """Get color for leave status"""
    colors = {
        'قيد الانتظار': 'warning',
        'موافق عليها': 'success',
        'مرفوضة': 'danger',
        'ملغية': 'secondary'
    }
    return colors.get(status.value, 'secondary')
