{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users"></i> إدارة الموظفين</h2>
        <div>
            {% if current_user.has_permission(Permission.ADD_EMPLOYEE) %}
            <a href="{{ url_for('employees.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة موظف جديد
            </a>
            {% endif %}
            {% if current_user.has_permission(Permission.IMPORT_EMPLOYEES) %}
            <a href="{{ url_for('employees.import_employees') }}" class="btn btn-success">
                <i class="fas fa-file-import"></i> استيراد بيانات
            </a>
            {% endif %}
            {% if current_user.has_permission(Permission.EXPORT_EMPLOYEES) %}
            <a href="{{ url_for('employees.export_employees') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}" class="btn btn-info">
                <i class="fas fa-file-export"></i> تصدير بيانات
            </a>
            {% endif %}

            {% if current_user.has_permission(Permission.VIEW_REPORTS) %}
            <div class="btn-group">
                <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-alt"></i> التقارير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='all') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-list"></i> تقرير جميع الموظفين
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='category') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-users"></i> تقرير حسب الفئة
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='unit') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-building"></i> تقرير حسب الوحدة
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='rank') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-medal"></i> تقرير حسب الرتبة
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='status') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-info-circle"></i> تقرير حسب الحالة
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='hire_date') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-calendar-plus"></i> تقرير حسب تاريخ التعيين
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', report_type='promotion_date') }}{% if search %}?search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if unit %}&unit={{ unit }}{% endif %}">
                        <i class="fas fa-calendar-check"></i> تقرير حسب تاريخ آخر ترقية
                    </a></li>
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-search"></i> بحث متقدم</h5>
        </div>
        <div class="card-body">
            <form id="employee-search-form" method="GET" action="{{ url_for('employees.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", value=search) }}
                    </div>
                    <div class="col-md-3 mb-3">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select", selected=status) }}
                    </div>
                    <div class="col-md-3 mb-3">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select", selected=category) }}
                    </div>
                    <div class="col-md-3 mb-3">
                        {{ form.unit.label(class="form-label") }}
                        {{ form.unit(class="form-control", value=unit) }}
                    </div>
                </div>
                <div class="text-center">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Employees List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> قائمة الموظفين</h5>
        </div>
        <div class="card-body">
            {% if employees.items %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الرقم العسكري</th>
                            <th>الاسم</th>
                            <th>الرتبة</th>
                            <th>الوحدة</th>
                            <th>العمل المكلف به</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees.items %}
                        <tr>
                            <td>{{ employee.military_id }}</td>
                            <td>{{ employee.name }}</td>
                            <td>{{ employee.military_rank }}</td>
                            <td>{{ employee.unit }}</td>
                            <td>{{ employee.position }}</td>
                            <td>
                                {% if employee.status.name == 'ACTIVE' %}
                                <span class="badge bg-success">مستمر</span>
                                {% elif employee.status.name == 'ABSENT' %}
                                <span class="badge bg-danger">غائب/هارب</span>
                                {% elif employee.status.name == 'ASSIGNED' %}
                                <span class="badge bg-info">منتدب</span>
                                {% elif employee.status.name == 'ON_LEAVE' %}
                                <span class="badge bg-warning">في إجازة</span>
                                {% elif employee.status.name == 'SUSPENDED' %}
                                <span class="badge bg-secondary">موقوف</span>
                                {% elif employee.status.name == 'SCATTERED' %}
                                <span class="badge bg-primary">متفرق</span>
                                {% elif employee.status.name == 'MEDICAL' %}
                                <span class="badge bg-dark">عيادة طبية</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('employees.print_employee', id=employee.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ employee.id }}" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ employee.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ employee.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ employee.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف الموظف <strong>{{ employee.name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('employees.delete', id=employee.id) }}" method="POST">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if employees.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, search=search, status=status, category=category, unit=unit) }}">السابق</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    {% endif %}

                    {% for page_num in employees.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == employees.page %}
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="{{ url_for('employees.index', page=page_num, search=search, status=status, category=category, unit=unit) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees.index', page=page_num, search=search, status=status, category=category, unit=unit) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if employees.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, search=search, status=status, category=category, unit=unit) }}">التالي</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا توجد بيانات موظفين متطابقة مع معايير البحث.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
