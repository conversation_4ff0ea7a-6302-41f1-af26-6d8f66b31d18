{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h2>
        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة المستخدمين
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-edit"></i> بيانات المستخدم</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.register') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {% if form.username.errors %}
                                {{ form.username(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.username(class="form-control") }}
                            {% endif %}
                            <div class="form-text">يجب أن يكون اسم المستخدم بين 3 و 64 حرفًا.</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {% if form.email.errors %}
                                {{ form.email(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.email(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                {{ form.password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.password(class="form-control") }}
                            {% endif %}
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل.</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {% if form.confirm_password.errors %}
                                {{ form.confirm_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.confirm_password(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.is_admin(class="form-check-input") }}
                            {{ form.is_admin.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('auth.users') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
