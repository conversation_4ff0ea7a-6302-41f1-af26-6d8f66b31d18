{% if notifications %}
<div class="dropdown-menu dropdown-menu-end notification-dropdown p-0" aria-labelledby="notificationDropdown">
    <div class="dropdown-header bg-primary text-white py-2 px-3 d-flex justify-content-between align-items-center">
        <span><i class="fas fa-bell me-1"></i> الإشعارات</span>
        <span class="badge bg-light text-primary rounded-pill">{{ notifications|length }}</span>
    </div>
    <div class="notification-list" style="max-height: 300px; overflow-y: auto;">
        {% if notifications %}
            {% for notification in notifications %}
                <a href="{{ notification.link }}" class="dropdown-item notification-item py-2 px-3 {% if not notification.is_read %}unread{% endif %}">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-2">
                            {% if notification.type == 'leave_approved' %}
                                <span class="notification-icon-circle bg-success">
                                    <i class="fas fa-check-circle text-white"></i>
                                </span>
                            {% elif notification.type == 'leave_rejected' %}
                                <span class="notification-icon-circle bg-danger">
                                    <i class="fas fa-times-circle text-white"></i>
                                </span>
                            {% elif notification.type == 'leave_request' %}
                                <span class="notification-icon-circle bg-info">
                                    <i class="fas fa-calendar-alt text-white"></i>
                                </span>
                            {% elif notification.type == 'system' %}
                                <span class="notification-icon-circle bg-primary">
                                    <i class="fas fa-info-circle text-white"></i>
                                </span>
                            {% else %}
                                <span class="notification-icon-circle bg-secondary">
                                    <i class="fas fa-bell text-white"></i>
                                </span>
                            {% endif %}
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-text small text-muted">{{ notification.message }}</div>
                            <div class="notification-time small text-muted mt-1">
                                <i class="fas fa-clock me-1"></i>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                        </div>
                    </div>
                </a>
            {% endfor %}
        {% else %}
            <div class="dropdown-item text-center py-3">
                <span class="text-muted">لا توجد إشعارات جديدة</span>
            </div>
        {% endif %}
    </div>
    <div class="dropdown-divider m-0"></div>
    <a href="{{ url_for('notifications.index') }}" class="dropdown-item text-center py-2">
        عرض جميع الإشعارات
    </a>
</div>
{% endif %}
