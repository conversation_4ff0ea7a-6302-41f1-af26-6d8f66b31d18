from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timezone

from . import notifications_bp
from .. import db
from ..models import Notification

@notifications_bp.route('/')
@login_required
def index():
    # Get all notifications for the current user
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(Notification.created_at.desc()).all()
    
    # Mark all as read
    for notification in notifications:
        if not notification.is_read:
            notification.is_read = True
    
    db.session.commit()
    
    return render_template('notifications/index.html', notifications=notifications)

@notifications_bp.route('/mark-read/<int:id>', methods=['POST'])
@login_required
def mark_read(id):
    notification = Notification.query.get_or_404(id)
    
    # Check if the notification belongs to the current user
    if notification.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح بالوصول'}), 403
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_bp.route('/mark-all-read', methods=['POST'])
@login_required
def mark_all_read():
    # Mark all notifications as read
    notifications = Notification.query.filter_by(user_id=current_user.id, is_read=False).all()
    
    for notification in notifications:
        notification.is_read = True
    
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_bp.route('/count')
@login_required
def count():
    # Get count of unread notifications
    count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    
    return jsonify({'count': count})

@notifications_bp.route('/latest')
@login_required
def latest():
    # Get latest 5 notifications
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(Notification.created_at.desc()).limit(5).all()
    
    # Convert to dict
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message,
            'type': notification.type,
            'link': notification.link,
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    return jsonify({'notifications': notifications_data})
