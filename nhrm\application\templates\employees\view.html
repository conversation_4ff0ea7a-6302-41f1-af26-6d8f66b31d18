{% extends 'layouts/base.html' %}

{% block title %}بيانات الموظف - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-user me-2"></i>بيانات الموظف: {{ employee.name }}
        </h5>
        <div>
            <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-light btn-sm">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
            <a href="{{ url_for('employees.print_employee', id=employee.id) }}" class="btn btn-light btn-sm ms-2" target="_blank">
                <i class="fas fa-print me-1"></i>طباعة البيانات
            </a>
            <a href="{{ url_for('employees.index') }}" class="btn btn-light btn-sm ms-2">
                <i class="fas fa-list me-1"></i>قائمة الموظفين
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 text-center mb-4">
                {% if employee.profile_image %}
                <img src="{{ url_for('static', filename='img/' + employee.profile_image) }}" alt="{{ employee.name }}" class="img-fluid rounded-circle mb-3" style="max-width: 200px; max-height: 200px;">
                {% else %}
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 200px; height: 200px;">
                    <i class="fas fa-user-circle text-secondary" style="font-size: 120px;"></i>
                </div>
                {% endif %}
                <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="60" height="60" class="mt-2">
                <h4>{{ employee.name }}</h4>
                <p class="badge bg-primary">{{ employee.military_rank }}</p>

                <!-- QR Code -->
                <div class="mt-3">
                    <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid" style="max-width: 150px;">
                    <p class="small text-muted mt-2">امسح الرمز لعرض بيانات الموظف</p>
                    <a href="{{ url_for('static', filename='qr_scanner.html') }}" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
                        <i class="fas fa-qrcode me-1"></i>فتح ماسح الرمز
                    </a>
                </div>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">الرقم العسكري</th>
                                    <td>{{ employee.military_id }}</td>
                                </tr>
                                <tr>
                                    <th>العمل المكلف به</th>
                                    <td>{{ employee.position }}</td>
                                </tr>
                                <tr>
                                    <th>الوحدة</th>
                                    <td>{{ employee.department_name }}</td>
                                </tr>
                                <tr>
                                    <th>الرقم الوطني</th>
                                    <td>{{ employee.national_id }}</td>
                                </tr>
                                <tr>
                                    <th>الفئة</th>
                                    <td>{{ employee.employee_category or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <th>حالة الموظف</th>
                                    <td>
                                        {% if employee.employee_status == 'مستمر' %}
                                            <span class="badge bg-success">مستمر</span>
                                        {% elif employee.employee_status == 'غياب وهروب' %}
                                            <span class="badge bg-danger">غياب وهروب</span>
                                        {% elif employee.employee_status == 'مكلف' %}
                                            <span class="badge bg-primary">مكلف</span>
                                        {% elif employee.employee_status == 'اجازة' %}
                                            <span class="badge bg-info">اجازة</span>
                                        {% elif employee.employee_status == 'ايقاف عن العمل' %}
                                            <span class="badge bg-warning text-dark">ايقاف عن العمل</span>
                                        {% elif employee.employee_status == 'متفرق' %}
                                            <span class="badge bg-secondary">متفرق</span>
                                        {% elif employee.employee_status == 'عيادة طبية' %}
                                            <span class="badge bg-info text-dark">عيادة طبية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if employee.status_notes %}
                                <tr>
                                    <th>ملاحظات حول الحالة</th>
                                    <td>{{ employee.status_notes }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>تاريخ التعيين</th>
                                    <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ آخر ترقية</th>
                                    <td>{{ employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير متوفر' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">رقم الهاتف</th>
                                    <td>{{ employee.phone or 'غير متوفر' }}</td>
                                </tr>
                                <tr>
                                    <th>البريد الإلكتروني</th>
                                    <td>{{ employee.email or 'غير متوفر' }}</td>
                                </tr>
                                <tr>
                                    <th>فصيلة الدم</th>
                                    <td>{{ employee.blood_type or 'غير متوفر' }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ الميلاد</th>
                                    <td>{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير متوفر' }}</td>
                                </tr>
                                <tr>
                                    <th>مكان الميلاد</th>
                                    <td>{{ employee.birth_place or 'غير متوفر' }}</td>
                                </tr>
                                <tr>
                                    <th>السكن الحالي</th>
                                    <td>{{ employee.address or 'غير متوفر' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">
                <i class="fas fa-graduation-cap me-2"></i>المعلومات التعليمية والمالية
            </h5>
            <div class="row mt-3">
                <div class="col-md-6">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th style="width: 40%;">المؤهل العلمي</th>
                                <td>{{ employee.education or 'غير متوفر' }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الحصول على المؤهل</th>
                                <td>{{ employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير متوفر' }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th style="width: 40%;">المصرف</th>
                                <td>{{ employee.bank_name or 'غير متوفر' }}</td>
                            </tr>
                            <tr>
                                <th>رقم حساب المصرف</th>
                                <td>{{ employee.bank_account or 'غير متوفر' }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Leave History -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">
                <i class="fas fa-history me-2"></i>سجل الإجازات
            </h5>
            {% if employee.leave_requests %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in employee.leave_requests %}
                        <tr>
                            <td>{{ leave.id }}</td>
                            <td>{{ leave.leave_type_rel.name }}</td>
                            <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave.total_days }}</td>
                            <td>
                                {% if leave.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif leave.status == 'approved' %}
                                <span class="badge bg-success">تمت الموافقة</span>
                                {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوض</span>
                                {% endif %}
                            </td>
                            <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد إجازات مسجلة لهذا الموظف.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
