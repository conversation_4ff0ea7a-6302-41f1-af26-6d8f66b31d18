{% extends 'layouts/base.html' %}

{% block title %}إحصائيات الإجازات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إحصائيات الإجازات</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>إحصائيات الإجازات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">الإجازات حسب النوع</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="leaveTypeChart"></canvas>
                                    </div>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>نوع الإجازة</th>
                                                    <th>العدد</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for type_name, count in leave_counts %}
                                                <tr>
                                                    <td>{{ type_name }}</td>
                                                    <td>{{ count }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">الإجازات حسب الحالة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="leaveStatusChart"></canvas>
                                    </div>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>حالة الإجازة</th>
                                                    <th>العدد</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for status, count in status_counts %}
                                                <tr>
                                                    <td>
                                                        {% if status == 'pending' %}
                                                            <span class="badge bg-warning">قيد الانتظار</span>
                                                        {% elif status == 'approved' %}
                                                            <span class="badge bg-success">تمت الموافقة</span>
                                                        {% elif status == 'rejected' %}
                                                            <span class="badge bg-danger">مرفوضة</span>
                                                        {% elif status == 'cancelled' %}
                                                            <span class="badge bg-secondary">ملغاة</span>
                                                        {% else %}
                                                            <span class="badge bg-info">{{ status }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ count }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">الإجازات حسب الشهر</h6>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="leaveMonthChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Leave Type Chart
        const typeCtx = document.getElementById('leaveTypeChart').getContext('2d');
        const typeLabels = [];
        const typeData = [];
        const typeColors = [];

        {% for type_name, count in leave_counts %}
            typeLabels.push('{{ type_name }}');
            typeData.push({{ count }});
            // Generate a color based on the index
            const hue = ({{ loop.index0 }} * 137.5) % 360;
            typeColors.push(`hsl(${hue}, 70%, 60%)`);
        {% endfor %}

        new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: typeLabels,
                datasets: [{
                    data: typeData,
                    backgroundColor: typeColors,
                    borderColor: typeColors.map(color => color.replace('60%', '50%')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Leave Status Chart
        const statusCtx = document.getElementById('leaveStatusChart').getContext('2d');
        const statusLabels = [];
        const statusData = [];
        const statusColors = {
            'pending': '#ffc107',
            'approved': '#28a745',
            'rejected': '#dc3545',
            'cancelled': '#6c757d'
        };
        const colors = [];

        {% for status, count in status_counts %}
            statusLabels.push('{{ status }}');
            statusData.push({{ count }});
            colors.push(statusColors['{{ status }}'] || '#17a2b8');
        {% endfor %}

        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: statusLabels.map(status => {
                    if (status === 'pending') return 'قيد الانتظار';
                    if (status === 'approved') return 'تمت الموافقة';
                    if (status === 'rejected') return 'مرفوضة';
                    if (status === 'cancelled') return 'ملغاة';
                    return status;
                }),
                datasets: [{
                    data: statusData,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace(')', ', 0.8)')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Leave Month Chart
        const monthCtx = document.getElementById('leaveMonthChart').getContext('2d');
        const monthLabels = {{ month_names|safe }};
        const monthData = {{ month_values|safe }};

        new Chart(monthCtx, {
            type: 'bar',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: 'عدد الإجازات',
                    data: monthData,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد الإجازات'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الشهر'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'توزيع الإجازات حسب الشهر',
                        font: {
                            size: 16
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
