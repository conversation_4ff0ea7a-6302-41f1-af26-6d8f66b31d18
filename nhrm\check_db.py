import sqlite3
import os

# قائمة بملفات قواعد البيانات المحتملة
db_files = ['app.db', 'hrm.db', 'instance/app.db', 'instance/hrm.db']

for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"الجداول الموجودة في {db_file}:")
            for table in tables:
                print(f"- {table[0]}")
                
                # عرض عدد السجلات في كل جدول
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    count = cursor.fetchone()[0]
                    print(f"  عدد السجلات: {count}")
                except sqlite3.Error as e:
                    print(f"  خطأ في عرض عدد السجلات: {e}")
            
            conn.close()
        except sqlite3.Error as e:
            print(f"خطأ في فتح قاعدة البيانات {db_file}: {e}")
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("\nالتحقق من المجلدات:")
for folder in ['instance', 'application', 'hrmsystem']:
    if os.path.exists(folder):
        print(f"المجلد موجود: {folder}")
        # التحقق من وجود ملفات قواعد بيانات في المجلد
        for file in os.listdir(folder):
            if file.endswith('.db'):
                print(f"  ملف قاعدة بيانات: {os.path.join(folder, file)}")
    else:
        print(f"المجلد غير موجود: {folder}")
