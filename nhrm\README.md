# نظام إدارة الموارد البشرية (HRM)

نظام إدارة الموارد البشرية هو تطبيق ويب مصمم لإدارة موظفي المؤسسة العسكرية، بما في ذلك معلوماتهم الشخصية، طلبات الإجازة، والإعدادات النظامية.

## المميزات

- إدارة بيانات الموظفين (إضافة، تعديل، حذف، عرض)
- إدارة طلبات الإجازات (تقديم، موافقة، رفض)
- إدارة المستخدمين والصلاحيات
- واجهة مستخدم باللغة العربية
- تصميم متجاوب يعمل على جميع الأجهزة
- لوحة تحكم تفاعلية مع رسوم بيانية

## المتطلبات

- Python 3.8+
- Flask 2.0+
- MySQL 5.7+
- مكتبات Python الإضافية:
  - Flask-SQLAlchemy
  - Flask-Login
  - Flask-WTF
  - PyMySQL

## التثبيت

1. قم بنسخ المستودع:

```bash
git clone https://github.com/yourusername/hrm-system.git
cd hrm-system
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

4. قم بتعديل ملف `config.py` لإعداد اتصال قاعدة البيانات:

```python
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://username:password@localhost/hrm_db'
```

5. قم بإنشاء قاعدة البيانات وتهيئتها:

```bash
# إنشاء قاعدة البيانات في MySQL
mysql -u root -p
CREATE DATABASE hrm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit;

# تهيئة قاعدة البيانات بالبيانات الأولية
python init_db.py
```

6. قم بتشغيل التطبيق:

```bash
python run.py
```

7. افتح المتصفح على العنوان: `http://localhost:5000`

## بيانات تسجيل الدخول الافتراضية

- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

## هيكل المشروع

```
/hrmsystem
│── /application
│   ├── __init__.py
│   ├── auth/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── employees/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── leaves/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── static/
│   │   ├── css/
│   │   │   ├── auth.css
│   │   │   ├── main.css
│   │   │   └── bootstrap.min.css
│   │   ├── js/
│   │   │   ├── auth.js
│   │   │   └── main.js
│   │   └── img/
│   │       ├── logo.png
│   │       └── bg-auth.jpg
│   ├── templates/
│   │   ├── auth/
│   │   │   ├── login.html
│   │   │   └── register.html
│   │   ├── employees/
│   │   ├── leaves/
│   │   ├── layouts/
│   │   │   ├── base.html
│   │   │   └── auth_base.html
│   │   └── dashboard.html
│   ├── models.py
│   └── utilities.py
│── config.py
│── requirements.txt
│── run.py
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال

اسمك - [@your_twitter](https://twitter.com/your_twitter) - <EMAIL>

رابط المشروع: [https://github.com/yourusername/hrm-system](https://github.com/yourusername/hrm-system)
