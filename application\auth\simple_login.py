from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from .. import db
from ..models import User, AuditLog
from datetime import datetime, timezone

simple_login_bp = Blueprint('simple_login', __name__)

@simple_login_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        print(f"Login attempt with username: {username}")
        
        # Find user by username or email
        user = User.query.filter_by(username=username).first()
        if not user:
            user = User.query.filter_by(email=username).first()
        
        if user:
            print(f"User found: {user.username}, checking password")
            if check_password_hash(user.password, password):
                print("Password correct, logging in")
                login_user(user)
                user.last_login = datetime.now(timezone.utc)
                
                # Create audit log
                log = AuditLog(
                    user_id=user.id,
                    action='login',
                    entity='User',
                    entity_id=user.id,
                    details='تسجيل دخول ناجح',
                    ip_address=request.remote_addr
                )
                db.session.add(log)
                db.session.commit()
                
                return redirect(url_for('dashboard.index'))
            else:
                print("Password incorrect")
                flash('كلمة المرور غير صحيحة', 'danger')
        else:
            print("User not found")
            flash('اسم المستخدم غير موجود', 'danger')
    
    return render_template('auth/simple_login.html')
