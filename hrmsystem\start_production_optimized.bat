@echo off
chcp 65001 >nul
title نظام إدارة الموارد البشرية - الإنتاج المحسن

echo ================================================================
echo           نظام إدارة الموارد البشرية - وضع الإنتاج المحسن
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo يرجى التأكد من تثبيت pip
    pause
    exit /b 1
)

echo ✓ تم العثور على Python و pip

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "ssl" mkdir ssl
if not exist "application\static\uploads" mkdir application\static\uploads
if not exist "application\static\uploads\employees" mkdir application\static\uploads\employees
if not exist "application\static\uploads\profile_pics" mkdir application\static\uploads\profile_pics

echo ✓ تم إنشاء المجلدات المطلوبة

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo.
echo جاري التحقق من المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo تحذير: قد تكون هناك مشكلة في تثبيت بعض المتطلبات
)

echo ✓ تم التحقق من المتطلبات

REM تعيين متغيرات البيئة للإنتاج
set FLASK_ENV=production
set FLASK_DEBUG=0
set PYTHONPATH=%cd%

REM تعيين إعدادات الأداء
set PYTHONOPTIMIZE=1
set PYTHONDONTWRITEBYTECODE=1

echo.
echo ================================================================
echo                        معلومات التشغيل
echo ================================================================
echo البيئة: الإنتاج (Production)
echo التاريخ والوقت: %date% %time%
echo المجلد: %cd%
echo ================================================================
echo.

REM عرض معلومات الاتصال
echo للوصول إلى النظام:
echo   - المتصفح: http://localhost:5000
echo   - الشبكة المحلية: http://[عنوان_IP]:5000
echo.
echo بيانات المدير الافتراضي:
echo   - اسم المستخدم: admin
echo   - كلمة المرور: admin123
echo.
echo ملاحظات مهمة:
echo   - تأكد من تغيير كلمة مرور المدير بعد أول تسجيل دخول
echo   - يتم حفظ السجلات في مجلد logs
echo   - يتم حفظ النسخ الاحتياطية في مجلد backups
echo.
echo للإيقاف: اضغط Ctrl+C
echo ================================================================
echo.

REM بدء التطبيق
echo بدء تشغيل النظام...
echo.

python run_production_optimized.py

REM في حالة الخروج غير المتوقع
echo.
echo ================================================================
echo تم إيقاف النظام
echo ================================================================
echo.

if errorlevel 1 (
    echo حدث خطأ أثناء تشغيل النظام
    echo يرجى مراجعة ملفات السجلات في مجلد logs
    echo.
)

echo اضغط أي مفتاح للخروج...
pause >nul
