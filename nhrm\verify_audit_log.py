import sqlite3
import os

def verify_audit_log(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_log'")
        if cursor.fetchone():
            print(f"جدول audit_log موجود في {db_path}")
            
            # التحقق من وجود سجلات بدون قيمة user_id
            cursor.execute("SELECT COUNT(*) FROM audit_log WHERE user_id IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"تم العثور على {null_count} سجل بدون قيمة user_id")
                
                # عرض السجلات التي تحتوي على قيم NULL في عمود user_id
                cursor.execute("SELECT id, action, entity, entity_id FROM audit_log WHERE user_id IS NULL")
                null_records = cursor.fetchall()
                
                for record in null_records:
                    print(f"السجل {record[0]}: Action={record[1]}, Entity={record[2]}, Entity ID={record[3]}")
            else:
                print("لا توجد سجلات بدون قيمة user_id")
                
            # التحقق من وجود سجلات بقيمة user_id = 0
            cursor.execute("SELECT COUNT(*) FROM audit_log WHERE user_id = 0")
            zero_count = cursor.fetchone()[0]
            
            if zero_count > 0:
                print(f"تم العثور على {zero_count} سجل بقيمة user_id = 0")
                
                # عرض السجلات التي تحتوي على قيم 0 في عمود user_id
                cursor.execute("SELECT id, action, entity, entity_id FROM audit_log WHERE user_id = 0")
                zero_records = cursor.fetchall()
                
                for record in zero_records:
                    print(f"السجل {record[0]}: Action={record[1]}, Entity={record[2]}, Entity ID={record[3]}")
            else:
                print("لا توجد سجلات بقيمة user_id = 0")
                
            # التحقق من وجود سجلات بقيمة user_id غير موجودة في جدول user
            cursor.execute("""
                SELECT a.id, a.user_id, a.action, a.entity, a.entity_id 
                FROM audit_log a 
                LEFT JOIN user u ON a.user_id = u.id 
                WHERE u.id IS NULL AND a.user_id IS NOT NULL
            """)
            invalid_records = cursor.fetchall()
            
            if invalid_records:
                print(f"تم العثور على {len(invalid_records)} سجل بقيمة user_id غير موجودة في جدول user")
                
                for record in invalid_records:
                    print(f"السجل {record[0]}: User ID={record[1]}, Action={record[2]}, Entity={record[3]}, Entity ID={record[4]}")
            else:
                print("جميع قيم user_id موجودة في جدول user")
                
            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM audit_log")
            total_count = cursor.fetchone()[0]
            
            print(f"إجمالي عدد السجلات في جدول audit_log: {total_count}")
            print(f"نسبة السجلات الصالحة: {((total_count - null_count - zero_count) / total_count) * 100:.2f}%")
        else:
            print(f"جدول audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في التحقق من جدول audit_log في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        verify_audit_log(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من التحقق من جدول audit_log")
