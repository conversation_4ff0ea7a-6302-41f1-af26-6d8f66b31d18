import sqlite3
from datetime import datetime

def update_user_table():
    """تحديث جدول المستخدمين لإضافة عمود updated_at"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # التحقق من وجود عمود updated_at
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'updated_at' not in column_names:
            print("إضافة عمود updated_at إلى جدول user...")
            cursor.execute("ALTER TABLE user ADD COLUMN updated_at DATETIME")
            
            # تحديث قيمة updated_at لجميع المستخدمين
            now = datetime.now().isoformat()
            cursor.execute("UPDATE user SET updated_at = ?", (now,))
            
            print("تم تحديث جدول user بنجاح!")
        else:
            print("عمود updated_at موجود بالفعل في جدول user")
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث جدول user: {e}")
        return False

if __name__ == "__main__":
    update_user_table()
