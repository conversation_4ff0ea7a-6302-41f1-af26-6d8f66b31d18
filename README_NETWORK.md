# تشغيل نظام إدارة الموارد البشرية على الشبكة المحلية

هذا الدليل يشرح كيفية تشغيل نظام إدارة الموارد البشرية على الشبكة المحلية بحيث يمكن الوصول إليه من أجهزة أخرى.

## المتطلبات

قبل البدء، تأكد من تثبيت المكتبات التالية:

```
pip install waitress
```

## طرق تشغيل النظام على الشبكة المحلية

### 1. تشغيل النظام في بيئة التطوير

لتشغيل النظام في بيئة التطوير مع إمكانية الوصول إليه من الشبكة المحلية:

```
python run.py
```

سيعمل النظام على المنفذ 5000 ويمكن الوصول إليه من خلال:
- `http://[عنوان_IP_الجهاز]:5000`
- `http://localhost:5000` (من نفس الجهاز فقط)

### 2. تشغيل النظام في بيئة الإنتاج

لتشغيل النظام في بيئة أكثر استقراراً وأماناً (مناسبة للاستخدام الفعلي):

```
python run_production.py
```

سيعمل النظام على المنفذ 8080 ويمكن الوصول إليه من خلال:
- `http://[عنوان_IP_الجهاز]:8080`
- `http://localhost:8080` (من نفس الجهاز فقط)

## معرفة عنوان IP الجهاز

### في نظام Windows

1. افتح موجه الأوامر (Command Prompt) بالضغط على `Win + R` ثم كتابة `cmd` والضغط على Enter
2. اكتب الأمر التالي واضغط Enter:
   ```
   ipconfig
   ```
3. ابحث عن `IPv4 Address` تحت قسم `Wireless LAN adapter Wi-Fi` أو `Ethernet adapter`

### في نظام Linux

1. افتح Terminal
2. اكتب الأمر التالي واضغط Enter:
   ```
   hostname -I
   ```

## ملاحظات هامة

1. تأكد من أن جدار الحماية (Firewall) يسمح بالاتصال على المنفذ 5000 أو 8080 حسب طريقة التشغيل.
2. في بيئة الإنتاج، يُفضل استخدام `run_production.py` لأنه يستخدم خادم Waitress الذي يوفر أداءً وأماناً أفضل.
3. إذا كنت تريد تغيير المنفذ، يمكنك تعديل قيمة `port` في ملف `run.py` أو `run_production.py`.
4. للوصول إلى النظام من أجهزة أخرى، استخدم عنوان IP الجهاز الذي يعمل عليه النظام.
