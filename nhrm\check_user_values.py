import sqlite3

def check_user_values():
    """التحقق من قيم جدول user"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # عرض بيانات المستخدمين
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print("بيانات المستخدمين:")
        for user in users:
            print("\nمستخدم:")
            for i, value in enumerate(user):
                print(f"  {column_names[i]}: {value}")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_user_values()
