from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import <PERSON>Field, PasswordField, SubmitField, BooleanField, SelectField, TextAreaField, SelectMultipleField, widgets
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError, Optional
from ..models import User, UserRole, UserStatus, Permission

class MultiCheckboxField(SelectMultipleField):
    widget = widgets.ListWidget(prefix_label=False)
    option_widget = widgets.CheckboxInput()

class UserForm(FlaskForm):
    username = <PERSON><PERSON>ield('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=64)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    role = SelectField('الصلاحية', choices=[(role.name, role.value) for role in UserRole], validators=[DataRequired()])
    status = SelectField('الحالة', choices=[(status.name, status.value) for status in UserStatus], validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6, max=128)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[Optional(), EqualTo('password')])
    profile_image = FileField('الصورة الشخصية', validators=[FileAllowed(['jpg', 'jpeg', 'png'])])
    submit = SubmitField('حفظ')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError('اسم المستخدم مستخدم بالفعل. الرجاء اختيار اسم مستخدم آخر.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. الرجاء استخدام بريد إلكتروني آخر.')

class UserSearchForm(FlaskForm):
    search = StringField('بحث', validators=[Optional()])
    role = SelectField('الصلاحية', choices=[('', 'الكل')] + [(role.name, role.value) for role in UserRole], validators=[Optional()])
    status = SelectField('الحالة', choices=[('', 'الكل')] + [(status.name, status.value) for status in UserStatus], validators=[Optional()])
    submit = SubmitField('بحث')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6, max=128)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', validators=[DataRequired(), EqualTo('new_password')])
    submit = SubmitField('تغيير كلمة المرور')

class ProfileForm(FlaskForm):
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    profile_image = FileField('الصورة الشخصية', validators=[FileAllowed(['jpg', 'jpeg', 'png'])])
    submit = SubmitField('تحديث الملف الشخصي')

class PermissionsForm(FlaskForm):
    # صلاحيات الموظفين
    employee_permissions = MultiCheckboxField('صلاحيات الموظفين', choices=[
        (Permission.VIEW_EMPLOYEES.name, Permission.VIEW_EMPLOYEES.value),
        (Permission.ADD_EMPLOYEE.name, Permission.ADD_EMPLOYEE.value),
        (Permission.EDIT_EMPLOYEE.name, Permission.EDIT_EMPLOYEE.value),
        (Permission.DELETE_EMPLOYEE.name, Permission.DELETE_EMPLOYEE.value),
        (Permission.EXPORT_EMPLOYEES.name, Permission.EXPORT_EMPLOYEES.value),
        (Permission.IMPORT_EMPLOYEES.name, Permission.IMPORT_EMPLOYEES.value),
        (Permission.PRINT_EMPLOYEE.name, Permission.PRINT_EMPLOYEE.value)
    ])

    # صلاحيات الإجازات
    leave_permissions = MultiCheckboxField('صلاحيات الإجازات', choices=[
        (Permission.VIEW_LEAVES.name, Permission.VIEW_LEAVES.value),
        (Permission.ADD_LEAVE.name, Permission.ADD_LEAVE.value),
        (Permission.EDIT_LEAVE.name, Permission.EDIT_LEAVE.value),
        (Permission.DELETE_LEAVE.name, Permission.DELETE_LEAVE.value),
        (Permission.APPROVE_LEAVE.name, Permission.APPROVE_LEAVE.value),
        (Permission.REJECT_LEAVE.name, Permission.REJECT_LEAVE.value)
    ])

    # صلاحيات المستخدمين
    user_permissions = MultiCheckboxField('صلاحيات المستخدمين', choices=[
        (Permission.VIEW_USERS.name, Permission.VIEW_USERS.value),
        (Permission.ADD_USER.name, Permission.ADD_USER.value),
        (Permission.EDIT_USER.name, Permission.EDIT_USER.value),
        (Permission.DELETE_USER.name, Permission.DELETE_USER.value),
        (Permission.MANAGE_PERMISSIONS.name, Permission.MANAGE_PERMISSIONS.value)
    ])

    # صلاحيات التقارير
    report_permissions = MultiCheckboxField('صلاحيات التقارير', choices=[
        (Permission.VIEW_REPORTS.name, Permission.VIEW_REPORTS.value),
        (Permission.EXPORT_REPORTS.name, Permission.EXPORT_REPORTS.value),
        (Permission.PRINT_REPORTS.name, Permission.PRINT_REPORTS.value)
    ])

    # صلاحيات النظام
    system_permissions = MultiCheckboxField('صلاحيات النظام', choices=[
        (Permission.VIEW_AUDIT_LOGS.name, Permission.VIEW_AUDIT_LOGS.value),
        (Permission.MANAGE_SYSTEM.name, Permission.MANAGE_SYSTEM.value)
    ])

    submit = SubmitField('حفظ الصلاحيات')
