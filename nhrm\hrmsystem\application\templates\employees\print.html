<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الموظف: {{ employee.name }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.rtl.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <style>
        @font-face {
            font-family: 'Times New Roman';
            src: url('{{ url_for("static", filename="fonts/times.ttf") }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Times New Roman';
            src: url('{{ url_for("static", filename="fonts/timesbd.ttf") }}') format('truetype');
            font-weight: bold;
            font-style: normal;
        }
        @page {
            size: A4 portrait;
            margin: 1cm 1cm 1.5cm 1cm; /* top right bottom left */
        }
        body {
            font-family: 'Times New Roman', serif;
            font-size: 14pt;
            line-height: 1.4;
            background-color: white;
            color: black;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }
        .no-print {
            display: none !important;
        }

        /* أزرار التحكم */
        .control-buttons {
            margin-bottom: 20px;
        }

        .btn-print, .btn-back, .btn-export {
            padding: 8px 20px;
            font-size: 14pt;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-print {
            background-color: #0056b3;
            color: white;
            border: none;
        }

        .btn-print:hover {
            background-color: #003d82;
        }

        .btn-back {
            background-color: #6c757d;
            color: white;
            border: none;
        }

        .btn-back:hover {
            background-color: #5a6268;
        }

        .btn-export {
            background-color: #28a745;
            color: white;
            border: none;
        }

        .btn-export:hover {
            background-color: #218838;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #0056b3;
        }
        .header h2 {
            font-size: 20pt;
            font-weight: bold;
            margin: 5px 0;
            color: #0056b3;
        }
        .header h3 {
            font-size: 18pt;
            font-weight: bold;
            margin: 5px 0;
            color: #0056b3;
        }
        .header h4 {
            font-size: 16pt;
            font-weight: bold;
            margin: 5px 0;
            color: #0056b3;
        }
        .employee-photo {
            text-align: center;
            margin-bottom: 10px;
            float: right;
        }
        .employee-photo img {
            width: 120px;
            height: 120px;
            border: 1px solid #0056b3;
            padding: 2px;
            background: white;
            object-fit: cover;
        }
        .qr-code {
            text-align: center;
            margin-bottom: 10px;
            float: left;
        }
        .qr-code img {
            width: 100px; /* 3cm */
            height: 100px; /* 3cm */
            border: 1px solid #0056b3;
            padding: 2px;
            background: white;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            table-layout: fixed; /* لضمان عدم تجاوز عرض الجدول لحدود الصفحة */
        }
        .data-table th, .data-table td {
            border: 1.5px solid #0056b3;
            padding: 8px 5px;
            text-align: right;
            word-wrap: break-word; /* للسماح بالتفاف النص الطويل */
            overflow: hidden;
        }
        .data-table th {
            background-color: #b8d1f3;
            font-weight: bold;
            font-size: 13pt;
            color: #0056b3;
        }
        .data-table td {
            font-size: 14pt;
        }
        .section-title {
            background-color: #e3f2fd;
            padding: 8px;
            font-weight: bold;
            text-align: center;
            border: 1px solid #0056b3;
            margin-bottom: 15px;
            color: #0056b3;
            font-size: 16pt;
            border-radius: 5px;
        }
        .signature {
            margin-top: 30px;
            text-align: center;
            display: flex;
            justify-content: space-between;
        }
        .signature p {
            margin-bottom: 15px;
            font-weight: bold;
            color: #0056b3;
            font-size: 16pt;
        }
        .signature-line {
            display: inline-block;
            width: 150px;
            border-bottom: 1px solid #0056b3;
            margin-bottom: 3px;
            margin-top: 40px;
        }
        .print-container {
            width: 21cm;
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 0;
            min-height: 29.7cm;
            position: relative;
            box-sizing: border-box;
        }
        .col-md-4 {
            width: 33.33%;
            float: right;
        }
        .col-md-6 {
            width: 50%;
            float: right;
        }
        .col-md-3 {
            width: 25%;
            float: right;
        }
        .col-md-9 {
            width: 75%;
            float: right;
        }
        .col-md-12 {
            width: 100%;
            float: right;
        }
        .row {
            display: block;
            width: 100%;
            clear: both;
        }
        .row:after {
            clear: both;
            content: "";
            display: table;
        }
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
            border: 1px solid #000;
        }
        .bg-success { background-color: #e8f5e9; color: #000; }
        .bg-danger { background-color: #ffebee; color: #000; }
        .bg-info { background-color: #e3f2fd; color: #000; }
        .bg-warning { background-color: #fff8e1; color: #000; }
        .bg-secondary { background-color: #eceff1; color: #000; }
        .bg-primary { background-color: #e3f2fd; color: #000; }
        .bg-dark { background-color: #eceff1; color: #000; }
        @media print {
            body {
                background-color: white;
                font-size: 14pt;
                line-height: 1.4;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .print-container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            .badge {
                border: 1px solid #0056b3 !important;
            }
            .bg-success {
                background-color: #e8f5e9 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-danger {
                background-color: #ffebee !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-info {
                background-color: #e3f2fd !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-warning {
                background-color: #fff8e1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-secondary {
                background-color: #eceff1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-primary {
                background-color: #e3f2fd !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .bg-dark {
                background-color: #eceff1 !important;
                color: black !important;
                border: 1px solid #0056b3 !important;
            }
            .no-print {
                display: none !important;
            }
            .qr-code {
                display: block !important;
                visibility: visible !important;
            }
            .qr-code img {
                display: block !important;
                visibility: visible !important;
            }
            .data-table th {
                background-color: #b8d1f3 !important;
                color:rgb(65, 38, 184) !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                font-size: 14pt;
            }
            .data-table td {
                font-size: 13pt;
                padding: 6px 4px;
            }
            .section-title {
                background-color: #e3f2fd !important;
                color: #0056b3 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .data-table th, .data-table td {
                border: 1px solid #0056b3 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="print-container">
        <!-- أزرار التحكم (تظهر فقط في الشاشة وليس عند الطباعة) -->
        <div class="control-buttons d-flex justify-content-between mb-4 no-print">
            <div>
                <button class="btn-print" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
                <a href="{{ url_for('employees.export_data', id=employee.id) }}" class="btn-export">
                    <i class="fas fa-file-excel me-1"></i> تصدير Excel
                </a>
            </div>
            <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn-back">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>

        <div class="header">
            <h2>وزارة الداخلية</h2>
            <h3> مديرية أمـــن الخمـــس</h3>
            <h4> قســم المــرور والتــراخيص الخمــس</h4>
        </div>

        <div class="row" style="margin-bottom: 20px;">
            <!-- صورة الموظف على اليمين -->
            <div class="employee-photo" style="width: 25%; float: right;">
                {% if employee.profile_image %}
                <img src="{{ url_for('static', filename='img/employees/' + employee.profile_image) }}" alt="صورة الموظف">
                {% else %}
                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="صورة الموظف">
                {% endif %}
            </div>

            <!-- كود QR على اليسار -->
            <div class="qr-code" style="width: 20%; float: left;">
                <img src="{{ qr_code }}" alt="QR Code">
                <p style="font-size: 8pt; text-align: center; margin-top: 5px;">امسح الرمز  </p>
            </div>

            <!-- عنوان في الوسط -->
            <div style="width: 50%; float: right; text-align: center;">
                <h3 style="margin-top: 30px; color: #0056b3;">الســـيد : {{ employee.name }}</h3>

            </div>
        </div>

        <div style="clear: both;"></div>

        <div class="section-title">البيانات الشخصية</div>

        <table class="data-table">
            <tr>
                <th width="20%">الاســــم</th>
                <td width="30%">{{ employee.name }}</td>
                <th width="20%">الرتـــبة</th>
                <td width="30%">{{ employee.military_rank or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>الرقم العسكري</th>
                <td>{{ employee.military_id }}</td>
                <th>الرقم الوطني</th>
                <td>{{ employee.national_id or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>تاريخ الميلاد</th>
                <td>{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير محدد' }}</td>
                <th>مكان الميلاد</th>
                <td>{{ employee.birth_place or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>فصيلة الدم</th>
                <td>{{ employee.blood_type or 'غير محدد' }}</td>
                <th>السكن الحالي</th>
                <td>{{ employee.current_residence or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">البيانات التعليمية والمهنية</div>

        <table class="data-table">
            <tr>
                <th width="20%">المؤهل العلمي</th>
                <td width="30%">{{ employee.education or 'غير محدد' }}</td>
                <th width="20%">تاريخ المؤهل</th>
                <td width="30%">{{ employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>الفـــئة</th>
                <td>{{ employee.category.value if employee.category else 'غير محدد' }}</td>
                <th>الوحــــدة</th>
                <td>{{ employee.unit }}</td>
            </tr>
            <tr>
                <th>العمل المكلف به</th>
                <td>{{ employee.position }}</td>
                <th>الحـــالة</th>
                <td>
                    <span class="badge
                        {% if employee.status.name == 'ACTIVE' %}bg-success
                        {% elif employee.status.name == 'ABSENT' %}bg-danger
                        {% elif employee.status.name == 'ASSIGNED' %}bg-info
                        {% elif employee.status.name == 'ON_LEAVE' %}bg-warning
                        {% elif employee.status.name == 'SUSPENDED' %}bg-secondary
                        {% elif employee.status.name == 'SCATTERED' %}bg-primary
                        {% elif employee.status.name == 'MEDICAL' %}bg-dark
                        {% endif %}">
                        {{ employee.status.value }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>تاريخ التعيين</th>
                <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد' }}</td>
                <th>تاريخ آخر ترقية</th>
                <td>{{ employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">بيانات الاتصال والبيانات المالية</div>

        <table class="data-table">
            <tr>
                <th width="20%">رقم الهاتف</th>
                <td width="30%">{{ employee.phone or 'غير محدد' }}</td>
                <th width="20%">البريد الإلكتروني</th>
                <td width="30%">{{ employee.email or 'غير محدد' }}</td>
            </tr>
            <tr>
                <th>المصرف</th>
                <td>{{ employee.bank_name or 'غير محدد' }}</td>
                <th>رقم الحساب المصرفي</th>
                <td>{{ employee.bank_account or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="section-title">بيانات الإجازات</div>

        <table class="data-table">
            <tr>
                <th width="20%">رصيد الإجازة</th>
                <td width="30%">{{ employee.leave_balance }} يوم</td>
                <th width="20%">ملاحظات الحالة</th>
                <td width="30%">{{ employee.status_notes or 'غير محدد' }}</td>
            </tr>
        </table>

        <div class="qr-code" style="position: absolute; bottom: 20px; left: 20px;">

        </div>

        <div class="report-footer" style="display: flex; justify-content: space-between; margin-top: 30px; border-top: 1px solid #0056b3; padding-top: 10px;">
            <div>
                <p style="font-weight: bold; font-size: 16pt;">يعتمد</p>
                <div style="margin-top: 40px; border-top: 1px solid #0056b3; width: 150px;"></div>
            </div>
            <div>
                <p style="font-weight: bold;">التاريخ: {{ now.strftime('%Y/%m/%d') }}</p>
                <p>اسم المستخدم: {{ current_user.full_name or current_user.username }}</p>
            </div>
        </div>

        <!-- رقم الصفحة -->
        <div class="page-number" style="text-align: center; margin-top: 15px; font-size: 11pt; color: #0056b3;">
            صفحة 1
        </div>

        <!-- Footer removed as requested -->
        </div> <!-- end of print-container -->
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
