{% extends 'layouts/base.html' %}

{% block title %}إدارة الموظفين - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="40" height="40" class="me-2">
            <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>إدارة الموظفين
            </h5>
        </div>
        <div>
            <button class="btn btn-light btn-sm me-2" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearch" aria-expanded="false" aria-controls="advancedSearch">
                <i class="fas fa-search me-1"></i>البحث المتقدم
            </button>
            <a href="{{ url_for('employees.import_employees') }}" class="btn btn-light btn-sm me-2">
                <i class="fas fa-file-import me-1"></i>استيراد بيانات
            </a>
            <a href="{{ url_for('employees.create') }}" class="btn btn-light btn-sm">
                <i class="fas fa-user-plus me-1"></i>إضافة موظف جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Collapsible Advanced Search -->
        <div class="collapse mb-4" id="advancedSearch">
            <div class="card card-body bg-light">
                <form method="GET" action="{{ url_for('employees.index') }}" class="row g-3">
                    <input type="hidden" name="advanced_search" value="true">
                    <div class="col-md-3">
                        <label for="name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="name" name="name" placeholder="الاسم" value="{{ name_query }}">
                    </div>
                    <div class="col-md-3">
                        <label for="military_id" class="form-label">الرقم العسكري</label>
                        <input type="text" class="form-control" id="military_id" name="military_id" placeholder="الرقم العسكري" value="{{ military_id_query }}">
                    </div>
                    <div class="col-md-3">
                        <label for="rank_query" class="form-label">الرتبة</label>
                        <input type="text" class="form-control" id="rank_query" name="rank_query" placeholder="الرتبة" value="{{ rank_query }}">
                    </div>
                    <div class="col-md-3">
                        <label for="unit" class="form-label">الوحدة</label>
                        <input type="text" class="form-control" id="unit" name="unit" placeholder="الوحدة" value="{{ unit_query }}">
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">الكل</option>
                            <option value="ضباط" {% if category_query == 'ضباط' %}selected{% endif %}>ضباط</option>
                            <option value="ضباط صف" {% if category_query == 'ضباط صف' %}selected{% endif %}>ضباط صف</option>
                            <option value="موظف" {% if category_query == 'موظف' %}selected{% endif %}>موظف</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">الكل</option>
                            <option value="مستمر" {% if status_query == 'مستمر' %}selected{% endif %}>مستمر</option>
                            <option value="غياب وهروب" {% if status_query == 'غياب وهروب' %}selected{% endif %}>غياب وهروب</option>
                            <option value="مكلف" {% if status_query == 'مكلف' %}selected{% endif %}>مكلف</option>
                            <option value="اجازة" {% if status_query == 'اجازة' %}selected{% endif %}>اجازة</option>
                            <option value="ايقاف عن العمل" {% if status_query == 'ايقاف عن العمل' %}selected{% endif %}>ايقاف عن العمل</option>
                            <option value="متفرق" {% if status_query == 'متفرق' %}selected{% endif %}>متفرق</option>
                            <option value="عيادة طبية" {% if status_query == 'عيادة طبية' %}selected{% endif %}>عيادة طبية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="position" class="form-label">العمل المكلف به</label>
                        <input type="text" class="form-control" id="position" name="position" placeholder="العمل المكلف به" value="{{ position_query }}">
                    </div>
                    <div class="col-md-3">
                        <label for="national_id" class="form-label">الرقم الوطني</label>
                        <input type="text" class="form-control" id="national_id" name="national_id" placeholder="الرقم الوطني" value="{{ national_id_query }}">
                    </div>
                    <div class="col-12 text-center mt-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                            <i class="fas fa-sync-alt me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Basic Search Form -->
        <form method="GET" action="{{ url_for('employees.index') }}" class="mb-4">
            <div class="row g-3">
                <div class="col-md-4">
                    {{ form.search.label(class="form-label") }}
                    {{ form.search(class="form-control", placeholder="بحث بالاسم أو الرقم العسكري", value=search_query) }}
                </div>
                <div class="col-md-3">
                    {{ form.department.label(class="form-label") }}
                    {{ form.department(class="form-control", placeholder="أدخل اسم الوحدة", value=department_query) }}
                </div>
                <div class="col-md-3">
                    {{ form.rank.label(class="form-label") }}
                    {{ form.rank(class="form-select") }}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    {{ form.submit(class="btn btn-primary w-100") }}
                </div>
            </div>
        </form>

        <!-- Employees Table -->
        <div class="card mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-1"></i>قائمة الموظفين
                    <small class="text-muted ms-2">عرض {{ employees.total }} موظف</small>
                </h6>
                <div>
                    <a href="{{ url_for('reports.export_report', report_type='employees') }}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-file-excel me-1"></i>تصدير Excel
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>الرقم العسكري</th>
                        <th>الاسم</th>
                        <th>الرتبة</th>
                        <th>العمل المكلف به</th>
                        <th>الوحدة</th>
                        <th>الحالة</th>
                        <th>تاريخ التعيين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>{{ employee.military_id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.military_rank }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department_name }}</td>
                        <td>
                            {% if employee.employee_status == 'مستمر' %}
                                <span class="badge bg-success">مستمر</span>
                            {% elif employee.employee_status == 'غياب وهروب' %}
                                <span class="badge bg-danger">غياب وهروب</span>
                            {% elif employee.employee_status == 'مكلف' %}
                                <span class="badge bg-primary">مكلف</span>
                            {% elif employee.employee_status == 'اجازة' %}
                                <span class="badge bg-info">اجازة</span>
                            {% elif employee.employee_status == 'ايقاف عن العمل' %}
                                <span class="badge bg-warning text-dark">ايقاف عن العمل</span>
                            {% elif employee.employee_status == 'متفرق' %}
                                <span class="badge bg-secondary">متفرق</span>
                            {% elif employee.employee_status == 'عيادة طبية' %}
                                <span class="badge bg-info text-dark">عيادة طبية</span>
                            {% else %}
                                <span class="badge bg-secondary">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-sm btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" title="حذف" data-bs-toggle="modal" data-bs-target="#deleteModal{{ employee.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ employee.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ employee.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteModalLabel{{ employee.id }}">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من حذف الموظف <strong>{{ employee.name }}</strong>؟
                                            <br>
                                            <span class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</span>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('employees.delete', id=employee.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">لا يوجد موظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if employees.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if employees.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, search=search_query, department=department_query, rank=rank_id, advanced_search=advanced_search, name=name_query, military_id=military_id_query, rank_query=rank_query, unit=unit_query, category=category_query, status=status_query, position=position_query, national_id=national_id_query) }}">السابق</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                </li>
                {% endif %}

                {% for page_num in employees.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page_num %}
                        {% if page_num == employees.page %}
                        <li class="page-item active" aria-current="page">
                            <a class="page-link" href="#">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employees.index', page=page_num, search=search_query, department=department_query, rank=rank_id, advanced_search=advanced_search, name=name_query, military_id=military_id_query, rank_query=rank_query, unit=unit_query, category=category_query, status=status_query, position=position_query, national_id=national_id_query) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if employees.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, search=search_query, department=department_query, rank=rank_id, advanced_search=advanced_search, name=name_query, military_id=military_id_query, rank_query=rank_query, unit=unit_query, category=category_query, status=status_query, position=position_query, national_id=national_id_query) }}">التالي</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Set selected values for dropdowns
    document.addEventListener('DOMContentLoaded', function() {
        const departmentSelect = document.getElementById('department');
        const rankSelect = document.getElementById('rank');

        if (departmentSelect) {
            // No longer needed as department is now a text input
    // departmentSelect.value = "{{ department_query or '' }}";
        }

        if (rankSelect) {
            rankSelect.value = "{{ rank_id or 0 }}";
        }

        // Show advanced search if it was used
        if ('{{ advanced_search }}' === 'True') {
            const advancedSearchCollapse = new bootstrap.Collapse(document.getElementById('advancedSearch'), {
                toggle: true
            });
        }
    });
</script>
{% endblock %}
