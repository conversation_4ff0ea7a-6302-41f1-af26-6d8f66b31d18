from application import create_app, db
from application.models import User, Employee, LeaveType, LeaveRequest, AuditLog
from werkzeug.security import generate_password_hash

app = create_app()

# Create a context for database operations
with app.app_context():
    # Create all tables
    db.create_all()
    
    # Check if admin user exists, if not create one
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            password=generate_password_hash('admin123'),
            is_admin=True
        )
        db.session.add(admin)
        db.session.commit()
        print('Admin user created!')
    
    # Check if leave types exist, if not create default ones
    if not LeaveType.query.first():
        leave_types = [
            LeaveType(name='إجازة سنوية', description='إجازة سنوية مدفوعة', color='#28a745'),
            LeaveType(name='إجازة مرضية', description='إجازة مرضية', color='#dc3545'),
            LeaveType(name='إجازة طارئة', description='إجازة طارئة', color='#ffc107'),
            LeaveType(name='إجازة بدون راتب', description='إجازة بدون راتب', color='#6c757d')
        ]
        db.session.bulk_save_objects(leave_types)
        db.session.commit()
        print('Default leave types created!')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
