#!/usr/bin/env python3
"""
نص تشغيل بسيط - نظام إدارة الموارد البشرية
Simple Startup Script - HR Management System

نص تشغيل مبسط بدون ميزات متقدمة
"""

import os
import sys
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    print("=" * 60)
    print("نظام إدارة الموارد البشرية")
    print("=" * 60)
    print(f"وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # إنشاء التطبيق
        from application import create_app
        app = create_app('development')
        
        print("✓ تم إنشاء التطبيق بنجاح")
        
        # إعدادات الخادم
        host = '0.0.0.0'
        port = 5000
        
        print(f"✓ بدء الخادم على {host}:{port}")
        print()
        print("للوصول إلى النظام:")
        print(f"  - المحلي: http://localhost:{port}")
        print(f"  - الشبكة: http://[عنوان_IP]:{port}")
        print()
        print("بيانات المدير الافتراضي:")
        print("  - اسم المستخدم: admin")
        print("  - كلمة المرور: admin123")
        print()
        print("للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        print()
        
        # تشغيل الخادم
        app.run(
            host=host,
            port=port,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
