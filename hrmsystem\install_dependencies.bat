@echo off
chcp 65001 >nul
title تثبيت متطلبات نظام إدارة الموارد البشرية

echo ================================================================
echo           تثبيت متطلبات نظام إدارة الموارد البشرية
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo يرجى التأكد من تثبيت pip مع Python
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على pip

echo.
echo جاري تثبيت المتطلبات الأساسية...
echo ================================================================

REM تحديث pip أولاً
echo تحديث pip...
python -m pip install --upgrade pip

REM تثبيت المتطلبات الأساسية
echo.
echo تثبيت Flask وملحقاته...
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.1.1
pip install Flask-Migrate==4.0.5
pip install Flask-Login==0.6.3
pip install Flask-WTF==1.2.1
pip install Werkzeug==2.3.7

echo.
echo تثبيت مكتبات معالجة الصور والبيانات...
pip install Pillow==10.1.0
pip install WTForms==3.1.1
pip install email-validator==2.1.0
pip install openpyxl==3.1.2
pip install qrcode==7.4.2

echo.
echo ================================================================
echo تم تثبيت المتطلبات الأساسية بنجاح!
echo ================================================================

echo.
echo هل تريد تثبيت المكتبات الاختيارية لمراقبة الأداء؟ (y/n)
set /p install_optional="اختر (y للموافقة، n للرفض): "

if /i "%install_optional%"=="y" (
    echo.
    echo تثبيت المكتبات الاختيارية...
    pip install psutil==5.9.6
    echo ✓ تم تثبيت psutil لمراقبة النظام
) else (
    echo تم تخطي المكتبات الاختيارية
)

echo.
echo ================================================================
echo                        تم الانتهاء من التثبيت
echo ================================================================
echo.

echo يمكنك الآن تشغيل النظام باستخدام:
echo   start_production_optimized.bat
echo.
echo أو:
echo   python run_production_optimized.py
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
