from flask import render_template, request, jsonify, send_file
from flask_login import login_required
from sqlalchemy import func, desc, case, extract
from io import BytesIO
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import base64
from datetime import datetime, timedelta
import calendar
import json

from . import reports_bp
from .. import db
from ..models import Employee, LeaveRequest, LeaveType

@reports_bp.route('/')
@login_required
def index():
    return render_template('reports/index.html')

@reports_bp.route('/employees-by-rank')
@login_required
def employees_by_rank():
    # Get employee count by rank
    rank_counts = db.session.query(
        Employee.military_rank,
        func.count(Employee.id)
    ).group_by(Employee.military_rank).all()

    # Sort by count descending
    rank_counts = sorted(rank_counts, key=lambda x: x[1], reverse=True)

    # Prepare data for chart
    labels = [rank for rank, _ in rank_counts]
    values = [count for _, count in rank_counts]

    return render_template('reports/employees_by_rank.html',
                          labels=json.dumps(labels),
                          values=json.dumps(values),
                          rank_counts=rank_counts)

@reports_bp.route('/employees-by-unit')
@login_required
def employees_by_unit():
    # Get employee count by unit
    unit_counts = db.session.query(
        Employee.department_name,
        func.count(Employee.id)
    ).group_by(Employee.department_name).all()

    # Sort by count descending
    unit_counts = sorted(unit_counts, key=lambda x: x[1], reverse=True)

    # Prepare data for chart
    labels = [unit for unit, _ in unit_counts]
    values = [count for _, count in unit_counts]

    return render_template('reports/employees_by_unit.html',
                          labels=json.dumps(labels),
                          values=json.dumps(values),
                          unit_counts=unit_counts)

@reports_bp.route('/employees-by-status')
@login_required
def employees_by_status():
    # Get employee count by status
    status_counts = db.session.query(
        Employee.employee_status,
        func.count(Employee.id)
    ).group_by(Employee.employee_status).all()

    # Sort by count descending
    status_counts = sorted(status_counts, key=lambda x: x[1], reverse=True)

    # Prepare data for chart
    labels = [status for status, _ in status_counts]
    values = [count for _, count in status_counts]

    # Define colors for each status
    status_colors = {
        'مستمر': '#28a745',  # green
        'غياب وهروب': '#dc3545',  # red
        'مكلف': '#007bff',  # blue
        'اجازة': '#17a2b8',  # cyan
        'ايقاف عن العمل': '#ffc107',  # yellow
        'متفرق': '#6c757d',  # gray
        'عيادة طبية': '#20c997'  # teal
    }

    # Get colors for chart
    colors = [status_colors.get(status, '#6c757d') for status in labels]

    return render_template('reports/employees_by_status.html',
                          labels=json.dumps(labels),
                          values=json.dumps(values),
                          colors=json.dumps(colors),
                          status_counts=status_counts)

@reports_bp.route('/employees-by-category')
@login_required
def employees_by_category():
    # Get employee count by category
    category_counts = db.session.query(
        Employee.employee_category,
        func.count(Employee.id)
    ).group_by(Employee.employee_category).all()

    # Sort by count descending
    category_counts = sorted(category_counts, key=lambda x: x[1], reverse=True)

    # Prepare data for chart
    labels = [category for category, _ in category_counts]
    values = [count for _, count in category_counts]

    return render_template('reports/employees_by_category.html',
                          labels=json.dumps(labels),
                          values=json.dumps(values),
                          category_counts=category_counts)

@reports_bp.route('/leave-requests')
@login_required
def leave_requests():
    # Get leave requests count by type
    leave_counts = db.session.query(
        LeaveType.name,
        func.count(LeaveRequest.id)
    ).join(LeaveRequest, LeaveRequest.leave_type_id == LeaveType.id)\
     .group_by(LeaveType.name).all()

    # Get leave requests count by status
    status_counts = db.session.query(
        LeaveRequest.status,
        func.count(LeaveRequest.id)
    ).group_by(LeaveRequest.status).all()

    # Get leave requests count by month
    month_counts = db.session.query(
        extract('month', LeaveRequest.start_date),
        func.count(LeaveRequest.id)
    ).group_by(extract('month', LeaveRequest.start_date)).all()

    # Convert month numbers to names
    month_names = [calendar.month_name[int(month)] for month, _ in month_counts]
    month_values = [count for _, count in month_counts]

    return render_template('reports/leave_requests.html',
                          leave_counts=leave_counts,
                          status_counts=status_counts,
                          month_names=json.dumps(month_names),
                          month_values=json.dumps(month_values))

@reports_bp.route('/comprehensive-report')
@login_required
def comprehensive_report():
    # Get all data for comprehensive report
    employees = Employee.query.all()

    # Count by rank
    rank_counts = {}
    for emp in employees:
        rank_counts[emp.military_rank] = rank_counts.get(emp.military_rank, 0) + 1

    # Count by unit
    unit_counts = {}
    for emp in employees:
        unit_counts[emp.department_name] = unit_counts.get(emp.department_name, 0) + 1

    # Count by status
    status_counts = {}
    for emp in employees:
        status_counts[emp.employee_status] = status_counts.get(emp.employee_status, 0) + 1

    # Count by category
    category_counts = {}
    for emp in employees:
        category_counts[emp.employee_category] = category_counts.get(emp.employee_category, 0) + 1

    # Add current time for report generation
    current_time = datetime.now()

    return render_template('reports/comprehensive_report.html',
                          employees=employees,
                          rank_counts=rank_counts,
                          unit_counts=unit_counts,
                          status_counts=status_counts,
                          category_counts=category_counts,
                          current_time=current_time)

@reports_bp.route('/export-report/<report_type>')
@login_required
def export_report(report_type):
    if report_type == 'employees':
        # Export all employees data
        employees = Employee.query.all()

        # Create DataFrame
        data = []
        for emp in employees:
            data.append({
                'الاسم': emp.name,
                'الرقم العسكري': emp.military_id,
                'الرقم الوطني': emp.national_id,
                'الرتبة': emp.military_rank,
                'الوحدة': emp.department_name,
                'العمل المكلف به': emp.position,
                'الفئة': emp.employee_category,
                'الحالة': emp.employee_status,
                'ملاحظات الحالة': emp.status_notes,
                'تاريخ التعيين': emp.hire_date,
                'تاريخ آخر ترقية': emp.last_promotion_date,
                'تاريخ الميلاد': emp.date_of_birth,
                'مكان الميلاد': emp.birth_place,
                'فصيلة الدم': emp.blood_type,
                'المؤهل العلمي': emp.education,
                'تاريخ المؤهل': emp.education_date,
                'المصرف': emp.bank_name,
                'رقم الحساب': emp.bank_account,
                'رقم الهاتف': emp.phone,
                'البريد الإلكتروني': emp.email,
                'السكن الحالي': emp.address
            })

        df = pd.DataFrame(data)

        # Create Excel file
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='الموظفين', index=False)

            # Auto-adjust columns' width
            worksheet = writer.sheets['الموظفين']
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
                worksheet.set_column(i, i, column_width)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'employees_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    elif report_type == 'by_status':
        # Export employees by status
        employees = Employee.query.all()

        # Group by status
        status_groups = {}
        for emp in employees:
            if emp.employee_status not in status_groups:
                status_groups[emp.employee_status] = []
            status_groups[emp.employee_status].append(emp)

        # Create Excel file with multiple sheets
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            for status, emps in status_groups.items():
                # Create DataFrame for this status
                data = []
                for emp in emps:
                    data.append({
                        'الاسم': emp.name,
                        'الرقم العسكري': emp.military_id,
                        'الرتبة': emp.military_rank,
                        'الوحدة': emp.department_name,
                        'العمل المكلف به': emp.position,
                        'الفئة': emp.employee_category,
                        'ملاحظات الحالة': emp.status_notes,
                        'تاريخ التعيين': emp.hire_date,
                        'رقم الهاتف': emp.phone
                    })

                if data:  # Only create sheet if there's data
                    df = pd.DataFrame(data)
                    sheet_name = status if status else 'غير محدد'
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # Auto-adjust columns' width
                    worksheet = writer.sheets[sheet_name]
                    for i, col in enumerate(df.columns):
                        column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
                        worksheet.set_column(i, i, column_width)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'employees_by_status_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    elif report_type == 'by_category':
        # Export employees by category
        employees = Employee.query.all()

        # Group by category
        category_groups = {}
        for emp in employees:
            if emp.employee_category not in category_groups:
                category_groups[emp.employee_category] = []
            category_groups[emp.employee_category].append(emp)

        # Create Excel file with multiple sheets
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            for category, emps in category_groups.items():
                # Create DataFrame for this category
                data = []
                for emp in emps:
                    data.append({
                        'الاسم': emp.name,
                        'الرقم العسكري': emp.military_id,
                        'الرتبة': emp.military_rank,
                        'الوحدة': emp.department_name,
                        'العمل المكلف به': emp.position,
                        'الحالة': emp.employee_status,
                        'تاريخ التعيين': emp.hire_date,
                        'رقم الهاتف': emp.phone
                    })

                if data:  # Only create sheet if there's data
                    df = pd.DataFrame(data)
                    sheet_name = category if category else 'غير محدد'
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # Auto-adjust columns' width
                    worksheet = writer.sheets[sheet_name]
                    for i, col in enumerate(df.columns):
                        column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
                        worksheet.set_column(i, i, column_width)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'employees_by_category_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    # Default case
    return jsonify({'error': 'Invalid report type'}), 400
