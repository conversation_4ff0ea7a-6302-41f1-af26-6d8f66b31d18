{% extends 'layouts/base.html' %}

{% block title %}سجلات التدقيق - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .audit-timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .audit-timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }
    
    .audit-item {
        position: relative;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .audit-item:last-child {
        border-bottom: none;
    }
    
    .audit-dot {
        position: absolute;
        left: -30px;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #007bff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }
    
    .audit-dot-create {
        background-color: #28a745;
    }
    
    .audit-dot-update {
        background-color: #007bff;
    }
    
    .audit-dot-delete {
        background-color: #dc3545;
    }
    
    .audit-dot-login {
        background-color: #6c757d;
    }
    
    .audit-time {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .audit-user {
        font-weight: bold;
    }
    
    .audit-action {
        margin-top: 5px;
    }
    
    .audit-details {
        margin-top: 10px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        font-size: 0.9rem;
    }
    
    .audit-filter {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>سجلات التدقيق
        </h5>
        <div>
            <button class="btn btn-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#auditFilter" aria-expanded="false" aria-controls="auditFilter">
                <i class="fas fa-filter me-1"></i>تصفية
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="collapse mb-4" id="auditFilter">
            <div class="audit-filter">
                <form method="GET" action="{{ url_for('audit.index') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="user" class="form-label">المستخدم</label>
                            <select name="user_id" id="user" class="form-select">
                                <option value="">الكل</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {% if request.args.get('user_id')|int == user.id %}selected{% endif %}>{{ user.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="action" class="form-label">الإجراء</label>
                            <select name="action" id="action" class="form-select">
                                <option value="">الكل</option>
                                <option value="create" {% if request.args.get('action') == 'create' %}selected{% endif %}>إنشاء</option>
                                <option value="update" {% if request.args.get('action') == 'update' %}selected{% endif %}>تعديل</option>
                                <option value="delete" {% if request.args.get('action') == 'delete' %}selected{% endif %}>حذف</option>
                                <option value="login" {% if request.args.get('action') == 'login' %}selected{% endif %}>تسجيل دخول</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="entity_type" class="form-label">نوع الكيان</label>
                            <select name="entity_type" id="entity_type" class="form-select">
                                <option value="">الكل</option>
                                <option value="Employee" {% if request.args.get('entity_type') == 'Employee' %}selected{% endif %}>موظف</option>
                                <option value="LeaveRequest" {% if request.args.get('entity_type') == 'LeaveRequest' %}selected{% endif %}>طلب إجازة</option>
                                <option value="User" {% if request.args.get('entity_type') == 'User' %}selected{% endif %}>مستخدم</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_range" class="form-label">الفترة الزمنية</label>
                            <select name="date_range" id="date_range" class="form-select">
                                <option value="">الكل</option>
                                <option value="today" {% if request.args.get('date_range') == 'today' %}selected{% endif %}>اليوم</option>
                                <option value="yesterday" {% if request.args.get('date_range') == 'yesterday' %}selected{% endif %}>الأمس</option>
                                <option value="week" {% if request.args.get('date_range') == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                                <option value="month" {% if request.args.get('date_range') == 'month' %}selected{% endif %}>هذا الشهر</option>
                            </select>
                        </div>
                        <div class="col-12 text-center mt-3">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>تصفية
                            </button>
                            <a href="{{ url_for('audit.index') }}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="audit-timeline">
            {% for log in logs.items %}
            <div class="audit-item">
                <div class="audit-dot audit-dot-{{ log.action }}">
                    {% if log.action == 'create' %}
                    <i class="fas fa-plus"></i>
                    {% elif log.action == 'update' %}
                    <i class="fas fa-edit"></i>
                    {% elif log.action == 'delete' %}
                    <i class="fas fa-trash"></i>
                    {% elif log.action == 'login' %}
                    <i class="fas fa-sign-in-alt"></i>
                    {% else %}
                    <i class="fas fa-cog"></i>
                    {% endif %}
                </div>
                <div class="audit-time">{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                <div class="audit-user">{{ log.user.username }}</div>
                <div class="audit-action">
                    {% if log.action == 'create' %}
                    قام بإنشاء
                    {% elif log.action == 'update' %}
                    قام بتعديل
                    {% elif log.action == 'delete' %}
                    قام بحذف
                    {% elif log.action == 'login' %}
                    قام بتسجيل الدخول
                    {% else %}
                    {{ log.action }}
                    {% endif %}
                    
                    {% if log.entity_type == 'Employee' %}
                    موظف
                    {% elif log.entity_type == 'LeaveRequest' %}
                    طلب إجازة
                    {% elif log.entity_type == 'User' %}
                    مستخدم
                    {% else %}
                    {{ log.entity_type }}
                    {% endif %}
                    
                    {% if log.entity_id %}
                    ({{ log.entity_id }})
                    {% endif %}
                </div>
                {% if log.details %}
                <div class="audit-details">
                    <pre class="mb-0">{{ log.details }}</pre>
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-history fa-3x mb-3 text-muted"></i>
                <p class="text-muted">لا توجد سجلات تدقيق متاحة</p>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if logs.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if logs.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('audit.index', page=logs.prev_num, **request.args) }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in logs.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page_num %}
                        {% if logs.page == page_num %}
                        <li class="page-item active">
                            <a class="page-link" href="{{ url_for('audit.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('audit.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if logs.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('audit.index', page=logs.next_num, **request.args) }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}
