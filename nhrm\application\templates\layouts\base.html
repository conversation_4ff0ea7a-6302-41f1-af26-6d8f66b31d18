<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الموارد البشرية{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                    <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="50" height="50" class="d-inline-block align-text-top me-2">
                    نظام إدارة الموارد البشرية
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('dashboard') %}active{% endif %}" href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('employees') %}active{% endif %}" href="{{ url_for('employees.index') }}">
                                <i class="fas fa-users me-1"></i> الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('leaves') %}active{% endif %}" href="{{ url_for('leaves.index') }}">
                                <i class="fas fa-calendar-alt me-1"></i> الإجازات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('reports') %}active{% endif %}" href="{{ url_for('reports.index') }}">
                                <i class="fas fa-chart-bar me-1"></i> التقارير
                            </a>
                        </li>
                        {% if current_user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('audit') %}active{% endif %}" href="{{ url_for('audit.index') }}">
                                <i class="fas fa-history me-1"></i> سجلات التدقيق
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.role == 'admin' %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cog me-1"></i> الإدارة
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.users') }}">
                                        <i class="fas fa-user-shield me-1"></i> المستخدمين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.register') }}">
                                        <i class="fas fa-user-plus me-1"></i> إضافة مستخدم
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                    </ul>
                    <div class="d-flex">
                        <!-- Notifications Dropdown -->
                        <div class="dropdown me-2">
                            <button class="btn btn-outline-light position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                {% set unread_count = current_user.notifications|selectattr('is_read', 'equalto', false)|list|length %}
                                {% if unread_count > 0 %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                    {{ unread_count }}
                                </span>
                                {% endif %}
                            </button>
                            {% include 'includes/notifications.html' with context %}
                        </div>

                        <!-- User Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i> {{ current_user.email }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('notifications.index') }}">
                                        <i class="fas fa-bell me-1"></i> الإشعارات
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-key me-1"></i> تغيير كلمة المرور
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="40" height="40" class="mb-2">
            <p class="mb-0">&copy; {{ now.year }} نظام إدارة الموارد البشرية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
