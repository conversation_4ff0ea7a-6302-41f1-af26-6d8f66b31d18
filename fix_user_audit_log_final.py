import sqlite3
import os
import sys

def fix_user_audit_log(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول user_audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_audit_log'")
        if cursor.fetchone():
            print(f"جدول user_audit_log موجود في {db_path}")
            
            # إنشاء جدول جديد بدون قيد NOT NULL على user_id
            try:
                # إنشاء جدول جديد بنفس الهيكل ولكن بدون قيد NOT NULL على user_id
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_audit_log_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action_by INTEGER,
                    action TEXT NOT NULL,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # نقل البيانات من الجدول الأصلي إلى الجدول الجديد
                cursor.execute("INSERT OR IGNORE INTO user_audit_log_new SELECT * FROM user_audit_log")
                
                # حذف الجدول الأصلي
                cursor.execute("DROP TABLE IF EXISTS user_audit_log")
                
                # إعادة تسمية الجدول الجديد
                cursor.execute("ALTER TABLE user_audit_log_new RENAME TO user_audit_log")
                
                print("تم تعديل هيكل الجدول بنجاح")
            except Exception as e:
                print(f"خطأ في تعديل هيكل الجدول: {e}")
            
            # إصلاح السجلات التي تحتوي على قيم NULL
            cursor.execute("UPDATE user_audit_log SET user_id = 1 WHERE user_id IS NULL")
            cursor.execute("UPDATE user_audit_log SET action_by = 1 WHERE action_by IS NULL")
            conn.commit()
            
            # التحقق من وجود سجلات بدون قيمة user_id
            cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE user_id IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"لا يزال هناك {null_count} سجل بدون قيمة user_id")
            else:
                print("لا توجد سجلات بدون قيمة user_id")
                
            # التحقق من وجود سجلات بدون قيمة action_by
            cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE action_by IS NULL")
            null_action_by_count = cursor.fetchone()[0]
            
            if null_action_by_count > 0:
                print(f"لا يزال هناك {null_action_by_count} سجل بدون قيمة action_by")
            else:
                print("لا توجد سجلات بدون قيمة action_by")
                
            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM user_audit_log")
            total_count = cursor.fetchone()[0]
            
            print(f"إجمالي عدد السجلات في جدول user_audit_log: {total_count}")
        else:
            print(f"جدول user_audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في إصلاح جدول user_audit_log في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        fix_user_audit_log(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من إصلاح جدول user_audit_log")
