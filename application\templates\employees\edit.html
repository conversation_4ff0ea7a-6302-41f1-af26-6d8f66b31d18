{% extends 'layouts/base.html' %}

{% block title %}تعديل بيانات الموظف - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.css">
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-edit me-2"></i>تعديل بيانات الموظف: {{ employee.name }}
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('employees.edit', id=employee.id) }}" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.name.label(class="form-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control", placeholder="أدخل الاسم الكامل") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.military_id.label(class="form-label") }}
                    {% if form.military_id.errors %}
                        {{ form.military_id(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.military_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.military_id(class="form-control", placeholder="أدخل الرقم العسكري") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.national_id.label(class="form-label") }}
                    {% if form.national_id.errors %}
                        {{ form.national_id(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.national_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.national_id(class="form-control", placeholder="أدخل الرقم الوطني") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.military_rank.label(class="form-label") }}
                    {% if form.military_rank.errors %}
                        {{ form.military_rank(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.military_rank.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.military_rank(class="form-control", placeholder="أدخل الرتبة العسكرية") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.position.label(class="form-label") }}
                    {% if form.position.errors %}
                        {{ form.position(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.position.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.position(class="form-control", placeholder="أدخل العمل المكلف به") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.department_name.label(class="form-label") }}
                    {% if form.department_name.errors %}
                        {{ form.department_name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.department_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.department_name(class="form-control", placeholder="أدخل اسم الوحدة") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.employee_category.label(class="form-label") }}
                    {% if form.employee_category.errors %}
                        {{ form.employee_category(class="form-select is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.employee_category.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.employee_category(class="form-select") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.blood_type.label(class="form-label") }}
                    {% if form.blood_type.errors %}
                        {{ form.blood_type(class="form-select is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.blood_type.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.blood_type(class="form-select") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.employee_status.label(class="form-label") }}
                    {% if form.employee_status.errors %}
                        {{ form.employee_status(class="form-select is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.employee_status.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.employee_status(class="form-select employee-status-select") }}
                    {% endif %}
                </div>
            </div>

            <div class="mb-3 status-notes-container" style="display: {% if employee.employee_status and employee.employee_status != 'مستمر' %}block{% else %}none{% endif %};">
                {{ form.status_notes.label(class="form-label") }}
                {% if form.status_notes.errors %}
                    {{ form.status_notes(class="form-control is-invalid", rows=2) }}
                    <div class="invalid-feedback">
                        {% for error in form.status_notes.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.status_notes(class="form-control", rows=2, placeholder="أدخل ملاحظات حول حالة الموظف") }}
                {% endif %}
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.birth_place.label(class="form-label") }}
                    {% if form.birth_place.errors %}
                        {{ form.birth_place(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.birth_place.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.birth_place(class="form-control", placeholder="أدخل مكان الميلاد") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.date_of_birth.label(class="form-label") }}
                    {% if form.date_of_birth.errors %}
                        {{ form.date_of_birth(class="form-control date-picker is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.date_of_birth.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.date_of_birth(class="form-control date-picker", placeholder="اختر تاريخ الميلاد") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.phone.label(class="form-label") }}
                    {% if form.phone.errors %}
                        {{ form.phone(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.phone.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.phone(class="form-control", placeholder="أدخل رقم الهاتف") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    {{ form.education.label(class="form-label") }}
                    {% if form.education.errors %}
                        {{ form.education(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.education.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.education(class="form-control", placeholder="أدخل المؤهل العلمي") }}
                    {% endif %}
                </div>
                <div class="col-md-4">
                    {{ form.education_date.label(class="form-label") }}
                    {% if form.education_date.errors %}
                        {{ form.education_date(class="form-control date-picker is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.education_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.education_date(class="form-control date-picker", placeholder="اختر تاريخ الحصول على المؤهل") }}
                    {% endif %}
                </div>
                <div class="col-md-2">
                    {{ form.hire_date.label(class="form-label") }}
                    {% if form.hire_date.errors %}
                        {{ form.hire_date(class="form-control date-picker is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.hire_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.hire_date(class="form-control date-picker", placeholder="اختر تاريخ التعيين") }}
                    {% endif %}
                </div>
                <div class="col-md-2">
                    {{ form.last_promotion_date.label(class="form-label") }}
                    {% if form.last_promotion_date.errors %}
                        {{ form.last_promotion_date(class="form-control date-picker is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.last_promotion_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.last_promotion_date(class="form-control date-picker", placeholder="اختر تاريخ آخر ترقية") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    {{ form.bank_name.label(class="form-label") }}
                    {% if form.bank_name.errors %}
                        {{ form.bank_name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.bank_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.bank_name(class="form-control", placeholder="أدخل اسم المصرف") }}
                    {% endif %}
                </div>
                <div class="col-md-6">
                    {{ form.bank_account.label(class="form-label") }}
                    {% if form.bank_account.errors %}
                        {{ form.bank_account(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.bank_account.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.bank_account(class="form-control", placeholder="أدخل رقم حساب المصرف") }}
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    {{ form.email.label(class="form-label") }}
                    {% if form.email.errors %}
                        {{ form.email(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.email.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.email(class="form-control", placeholder="أدخل البريد الإلكتروني") }}
                    {% endif %}
                </div>
                <div class="col-md-6">
                    {{ form.profile_image.label(class="form-label") }}
                    {% if form.profile_image.errors %}
                        {{ form.profile_image(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.profile_image.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.profile_image(class="form-control") }}
                        {% if employee.profile_image %}
                        <div class="form-text">
                            <img src="{{ url_for('static', filename='img/' + employee.profile_image) }}" alt="صورة الموظف" class="img-thumbnail mt-2" style="max-height: 100px;">
                            <span class="ms-2">الصورة الحالية</span>
                        </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>

            <div class="mb-3">
                {{ form.address.label(class="form-label") }}
                {% if form.address.errors %}
                    {{ form.address(class="form-control is-invalid", rows=3) }}
                    <div class="invalid-feedback">
                        {% for error in form.address.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.address(class="form-control", rows=3, placeholder="أدخل السكن الحالي") }}
                {% endif %}
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date pickers
        flatpickr(".date-picker", {
            locale: "ar",
            dateFormat: "Y-m-d",
            allowInput: true
        });

        // إظهار/إخفاء حقل الملاحظات بناءً على حالة الموظف
        const statusSelect = document.querySelector('.employee-status-select');
        const notesContainer = document.querySelector('.status-notes-container');

        if (statusSelect && notesContainer) {
            // التحقق من الحالة الأولية
            toggleNotesVisibility();

            // إضافة مستمع للتغييرات
            statusSelect.addEventListener('change', toggleNotesVisibility);

            function toggleNotesVisibility() {
                // إظهار حقل الملاحظات إذا كانت الحالة ليست "مستمر"
                if (statusSelect.value && statusSelect.value !== 'مستمر') {
                    notesContainer.style.display = 'block';

                    // إضافة ألوان مختلفة حسب الحالة
                    statusSelect.classList.remove('border-success', 'border-danger', 'border-primary', 'border-info', 'border-warning', 'border-secondary');

                    if (statusSelect.value === 'غياب وهروب') {
                        statusSelect.classList.add('border-danger');
                    } else if (statusSelect.value === 'مكلف') {
                        statusSelect.classList.add('border-primary');
                    } else if (statusSelect.value === 'اجازة') {
                        statusSelect.classList.add('border-info');
                    } else if (statusSelect.value === 'ايقاف عن العمل') {
                        statusSelect.classList.add('border-warning');
                    } else if (statusSelect.value === 'متفرق' || statusSelect.value === 'عيادة طبية') {
                        statusSelect.classList.add('border-secondary');
                    }
                } else {
                    notesContainer.style.display = 'none';
                    statusSelect.classList.remove('border-danger', 'border-primary', 'border-info', 'border-warning', 'border-secondary');
                    statusSelect.classList.add('border-success');
                }
            }
        }
    });
</script>
{% endblock %}
