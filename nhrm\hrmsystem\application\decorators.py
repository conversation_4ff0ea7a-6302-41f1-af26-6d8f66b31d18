from functools import wraps
from flask import flash, redirect, url_for
from flask_login import current_user
from .models import Permission

def permission_required(permission):
    """
    دالة للتحقق من وجود صلاحية محددة للمستخدم الحالي
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.has_permission(permission):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
                return redirect(url_for('dashboard.index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """
    دالة للتحقق من أن المستخدم الحالي هو مدير النظام
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function
