import sqlite3
import os

def verify_user_audit_log(db_path):
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول user_audit_log
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_audit_log'")
        if cursor.fetchone():
            print(f"جدول user_audit_log موجود في {db_path}")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(user_audit_log)")
            columns = cursor.fetchall()
            print("هيكل جدول user_audit_log:")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            # التحقق من وجود سجلات بدون قيمة user_id
            cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE user_id IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"تم العثور على {null_count} سجل بدون قيمة user_id")
                
                # عرض السجلات التي تحتوي على قيم NULL في عمود user_id
                cursor.execute("SELECT id, action, action_by FROM user_audit_log WHERE user_id IS NULL")
                null_records = cursor.fetchall()
                
                for record in null_records:
                    print(f"السجل {record[0]}: Action={record[1]}, Action By={record[2]}")
                    
                # إصلاح السجلات
                cursor.execute("UPDATE user_audit_log SET user_id = action_by WHERE user_id IS NULL")
                conn.commit()
                print(f"تم تحديث {cursor.rowcount} سجل بنجاح")
                
                # التحقق من نجاح التحديث
                cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE user_id IS NULL")
                null_count_after = cursor.fetchone()[0]
                if null_count_after == 0:
                    print("تم إصلاح جميع السجلات بنجاح")
                else:
                    print(f"لا يزال هناك {null_count_after} سجل بدون قيمة user_id")
            else:
                print("لا توجد سجلات بدون قيمة user_id")
                
            # التحقق من وجود سجلات بدون قيمة action_by
            cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE action_by IS NULL")
            null_action_by_count = cursor.fetchone()[0]
            
            if null_action_by_count > 0:
                print(f"تم العثور على {null_action_by_count} سجل بدون قيمة action_by")
                
                # عرض السجلات التي تحتوي على قيم NULL في عمود action_by
                cursor.execute("SELECT id, action, user_id FROM user_audit_log WHERE action_by IS NULL")
                null_action_by_records = cursor.fetchall()
                
                for record in null_action_by_records:
                    print(f"السجل {record[0]}: Action={record[1]}, User ID={record[2]}")
                    
                # إصلاح السجلات
                cursor.execute("UPDATE user_audit_log SET action_by = user_id WHERE action_by IS NULL")
                conn.commit()
                print(f"تم تحديث {cursor.rowcount} سجل بنجاح")
                
                # التحقق من نجاح التحديث
                cursor.execute("SELECT COUNT(*) FROM user_audit_log WHERE action_by IS NULL")
                null_action_by_count_after = cursor.fetchone()[0]
                if null_action_by_count_after == 0:
                    print("تم إصلاح جميع السجلات بنجاح")
                else:
                    print(f"لا يزال هناك {null_action_by_count_after} سجل بدون قيمة action_by")
            else:
                print("لا توجد سجلات بدون قيمة action_by")
                
            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM user_audit_log")
            total_count = cursor.fetchone()[0]
            
            print(f"إجمالي عدد السجلات في جدول user_audit_log: {total_count}")
        else:
            print(f"جدول user_audit_log غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في التحقق من جدول user_audit_log في {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        verify_user_audit_log(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من التحقق من جدول user_audit_log")
