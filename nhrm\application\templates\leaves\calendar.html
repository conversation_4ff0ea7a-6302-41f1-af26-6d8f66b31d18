{% extends 'layouts/base.html' %}

{% block title %}تقويم الإجازات - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
<style>
    .fc-event {
        cursor: pointer;
    }
    
    .fc-day-today {
        background-color: rgba(0, 123, 255, 0.1) !important;
    }
    
    .fc-day-sat, .fc-day-fri {
        background-color: rgba(220, 53, 69, 0.1);
    }
    
    .fc-holiday {
        background-color: rgba(255, 193, 7, 0.2) !important;
    }
    
    .fc-event-approved {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .fc-event-pending {
        background-color: #ffc107;
        border-color: #ffc107;
    }
    
    .fc-event-rejected {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .calendar-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }
    
    .legend-color {
        width: 15px;
        height: 15px;
        margin-right: 5px;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar-alt me-2"></i>تقويم الإجازات
        </h5>
        <a href="{{ url_for('leaves.create') }}" class="btn btn-light btn-sm">
            <i class="fas fa-plus me-1"></i>طلب إجازة جديد
        </a>
    </div>
    <div class="card-body">
        <div class="calendar-legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #28a745;"></div>
                <span>إجازة معتمدة</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffc107;"></div>
                <span>إجازة قيد الانتظار</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #dc3545;"></div>
                <span>إجازة مرفوضة</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(255, 193, 7, 0.2);"></div>
                <span>عطلة رسمية</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(220, 53, 69, 0.1);"></div>
                <span>عطلة أسبوعية (الجمعة والسبت)</span>
            </div>
        </div>
        
        <div id="calendar"></div>
    </div>
</div>

<!-- Leave Details Modal -->
<div class="modal fade" id="leaveDetailsModal" tabindex="-1" aria-labelledby="leaveDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveDetailsModalLabel">تفاصيل الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>الموظف:</strong>
                    <span id="leaveEmployee"></span>
                </div>
                <div class="mb-3">
                    <strong>نوع الإجازة:</strong>
                    <span id="leaveType"></span>
                </div>
                <div class="mb-3">
                    <strong>من تاريخ:</strong>
                    <span id="leaveStartDate"></span>
                </div>
                <div class="mb-3">
                    <strong>إلى تاريخ:</strong>
                    <span id="leaveEndDate"></span>
                </div>
                <div class="mb-3">
                    <strong>عدد الأيام:</strong>
                    <span id="leaveDays"></span>
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <span id="leaveStatus"></span>
                </div>
                <div class="mb-3">
                    <strong>السبب:</strong>
                    <p id="leaveReason"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="#" class="btn btn-primary" id="viewLeaveBtn">عرض التفاصيل</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/locales/ar.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const calendarEl = document.getElementById('calendar');
        
        // Initialize FullCalendar
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,listMonth'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                list: 'قائمة'
            },
            firstDay: 0, // Sunday is the first day
            weekends: true, // Show weekends
            events: {{ events|tojson }},
            eventClick: function(info) {
                // Show leave details in modal
                const event = info.event;
                const leaveId = event.id;
                
                // Set modal content
                document.getElementById('leaveEmployee').textContent = event.extendedProps.employee_name;
                document.getElementById('leaveType').textContent = event.extendedProps.leave_type;
                document.getElementById('leaveStartDate').textContent = formatDate(event.start);
                document.getElementById('leaveEndDate').textContent = formatDate(event.end);
                document.getElementById('leaveDays').textContent = event.extendedProps.days_count;
                document.getElementById('leaveReason').textContent = event.extendedProps.reason || 'غير محدد';
                
                // Set status with badge
                const statusElement = document.getElementById('leaveStatus');
                let statusHtml = '';
                
                if (event.extendedProps.status === 'approved') {
                    statusHtml = '<span class="badge bg-success">تمت الموافقة</span>';
                } else if (event.extendedProps.status === 'pending') {
                    statusHtml = '<span class="badge bg-warning text-dark">قيد الانتظار</span>';
                } else if (event.extendedProps.status === 'rejected') {
                    statusHtml = '<span class="badge bg-danger">مرفوضة</span>';
                }
                
                statusElement.innerHTML = statusHtml;
                
                // Set view button link
                document.getElementById('viewLeaveBtn').href = `/leaves/${leaveId}/view`;
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('leaveDetailsModal'));
                modal.show();
            },
            dayCellDidMount: function(info) {
                // Mark weekends (Friday and Saturday)
                if (info.date.getDay() === 5 || info.date.getDay() === 6) { // 5 is Friday, 6 is Saturday
                    info.el.classList.add('fc-day-weekend');
                }
                
                // Mark holidays
                const holidays = {{ holidays|tojson }};
                const dateStr = info.date.toISOString().split('T')[0];
                
                if (holidays.includes(dateStr)) {
                    info.el.classList.add('fc-holiday');
                }
            }
        });
        
        calendar.render();
        
        // Helper function to format date
        function formatDate(date) {
            if (!date) return '';
            
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            return new Date(date).toLocaleDateString('ar-EG', options);
        }
    });
</script>
{% endblock %}
