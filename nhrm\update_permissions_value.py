import sqlite3
import json

def update_permissions_value():
    """تحديث قيمة عمود permissions"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # تحديث جميع المستخدمين بقيمة افتراضية للصلاحيات
        print("تحديث جميع المستخدمين بقيمة افتراضية للصلاحيات...")
        cursor.execute("UPDATE user SET permissions = ?", (json.dumps([]),))
        
        conn.commit()
        print("تم تحديث قاعدة البيانات بنجاح!")
        
        # التحقق من التحديث
        cursor.execute("SELECT id, username, permissions FROM user")
        users = cursor.fetchall()
        
        print("\nالمستخدمين بعد التحديث:")
        for user in users:
            print(f"  {user[0]}: {user[1]} - permissions: {user[2]}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ: {e}")
        return False

if __name__ == "__main__":
    update_permissions_value()
