import sqlite3
import json

def fix_permissions():
    """إصلاح مشكلة الصلاحيات في قاعدة البيانات"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # إذا كان العمود موجودًا، قم بحذفه وإعادة إنشائه
        if 'permissions' in column_names:
            print("حذف العمود permissions الحالي...")
            # إنشاء جدول مؤقت بدون عمود permissions
            cursor.execute("""
                CREATE TABLE user_temp (
                    id INTEGER NOT NULL, 
                    username VARCHAR(64) NOT NULL, 
                    email VARCHAR(120) NOT NULL, 
                    password VARCHAR(128) NOT NULL, 
                    full_name VARCHAR(100), 
                    role VARCHAR(30), 
                    status VARCHAR(30), 
                    phone VARCHAR(20), 
                    profile_image VARCHAR(255), 
                    last_login DATETIME, 
                    is_admin BOOLEAN, 
                    created_at DATETIME, 
                    updated_at DATETIME,
                    PRIMARY KEY (id), 
                    UNIQUE (username), 
                    UNIQUE (email)
                )
            """)
            
            # نقل البيانات من الجدول الأصلي إلى الجدول المؤقت
            cursor.execute("""
                INSERT INTO user_temp 
                SELECT id, username, email, password, full_name, role, status, phone, 
                       profile_image, last_login, is_admin, created_at, updated_at
                FROM user
            """)
            
            # حذف الجدول الأصلي
            cursor.execute("DROP TABLE user")
            
            # إعادة تسمية الجدول المؤقت
            cursor.execute("ALTER TABLE user_temp RENAME TO user")
            
            print("تم إعادة إنشاء الجدول بنجاح!")
        
        # إضافة عمود permissions
        print("إضافة عمود permissions إلى جدول user...")
        cursor.execute("ALTER TABLE user ADD COLUMN permissions TEXT")
        
        # تحديث جميع المستخدمين بقيمة افتراضية للصلاحيات
        print("تحديث جميع المستخدمين بقيمة افتراضية للصلاحيات...")
        cursor.execute("UPDATE user SET permissions = ?", (json.dumps([]),))
        
        conn.commit()
        print("تم تحديث قاعدة البيانات بنجاح!")
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ: {e}")
        return False

if __name__ == "__main__":
    fix_permissions()
