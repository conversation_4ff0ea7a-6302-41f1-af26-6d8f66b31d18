{% extends 'layouts/base_print.html' %}

{% block content %}
<div class="report-container">
    <!-- أزرار التحكم (تظهر فقط في الشاشة وليس عند الطباعة) -->
    <div class="d-flex justify-content-between mb-4 no-print">
        <button class="btn btn-primary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-secondary btn-back">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </button>
    </div>
    
    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="report-title">وزارة الداخلية</div>
        <div class="report-subtitle">مديرية أمن {{ unit_name|default('الخمس') }}</div>
        <div class="report-subtitle">قسم المرور والتراخيص {{ unit_name|default('الخمس') }}</div>
        <div class="mt-4">كشف عن: {{ report_title|default('...............') }}</div>
    </div>
    
    <!-- جدول التقرير -->
    <table class="report-table report-rank">
        <thead>
            <tr>
                <th width="5%">م</th>
                <th width="15%">الرقم العسكري</th>
                <th width="15%">الرتبة</th>
                <th width="45%">الاسم</th>
                <th width="10%">الرقم الوطني</th>
                <th width="10%">ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            {% if employees %}
                {% for employee in employees %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ employee.military_id }}</td>
                    <td>{{ employee.military_rank }}</td>
                    <td>{{ employee.name }}</td>
                    <td>{{ employee.national_id }}</td>
                    <td>{{ employee.status_notes }}</td>
                </tr>
                {% endfor %}
            {% else %}
                {% for i in range(1, 11) %}
                <tr>
                    <td>{{ i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
    
    <!-- تذييل التقرير -->
    <div class="report-footer">
        <div>
            <p>يعتمد</p>
        </div>
        <div>
            <p>التاريخ: {{ now.strftime('%Y/%m/%d') }}</p>
            <p>اسم المستخدم: {{ current_user.full_name or current_user.username }}</p>
        </div>
    </div>
    
    <!-- رقم الصفحة -->
    <div class="page-number">
        {{ page_number|default('1') }}
    </div>
</div>
{% endblock %}
