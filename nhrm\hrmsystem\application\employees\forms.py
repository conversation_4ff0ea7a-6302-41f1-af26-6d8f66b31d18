from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, IntegerField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Optional, Length, ValidationError
from ..models import Employee, EmployeeCategory, EmployeeStatus
from datetime import date

class EmployeeForm(FlaskForm):
    military_id = StringField('الرقم العسكري', validators=[DataRequired(message='هذا الحقل مطلوب')])
    name = StringField('الاسم', validators=[DataRequired(message='هذا الحقل مطلوب')])
    national_id = StringField('الرقم الوطني', validators=[Optional()])
    blood_type = SelectField('فصيلة الدم', choices=[
        ('', 'اختر فصيلة الدم'),
        ('A+', 'A+'), ('A-', 'A-'),
        ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'),
        ('O+', 'O+'), ('O-', 'O-')
    ], validators=[Optional()])
    date_of_birth = DateField('تاريخ الميلاد', format='%Y-%m-%d', validators=[Optional()])
    birth_place = StringField('مكان الميلاد', validators=[Optional()])
    current_residence = StringField('السكن الحالي', validators=[Optional()])
    education = StringField('المؤهل العلمي', validators=[Optional()])
    education_date = DateField('تاريخ الحصول على المؤهل', format='%Y-%m-%d', validators=[Optional()])
    category = SelectField('الفئة', choices=[
        ('', 'اختر الفئة'),
        (EmployeeCategory.OFFICER.name, 'ضباط'),
        (EmployeeCategory.NCO.name, 'ضباط صف'),
        (EmployeeCategory.EMPLOYEE.name, 'موظف')
    ], validators=[Optional()])
    bank_name = StringField('المصرف', validators=[Optional()])
    bank_account = StringField('رقم الحساب المصرفي', validators=[Optional()])
    military_rank = StringField('الرتبة', validators=[Optional()])
    unit = StringField('الوحدة', validators=[DataRequired(message='هذا الحقل مطلوب')])
    position = StringField('العمل المكلف به', validators=[DataRequired(message='هذا الحقل مطلوب')])
    status = SelectField('الحالة', choices=[
        (EmployeeStatus.ACTIVE.name, 'مستمر'),
        (EmployeeStatus.ABSENT.name, 'غائب/هارب'),
        (EmployeeStatus.ASSIGNED.name, 'منتدب'),
        (EmployeeStatus.ON_LEAVE.name, 'في إجازة'),
        (EmployeeStatus.SUSPENDED.name, 'موقوف'),
        (EmployeeStatus.SCATTERED.name, 'متفرق'),
        (EmployeeStatus.MEDICAL.name, 'عيادة طبية')
    ], validators=[DataRequired(message='هذا الحقل مطلوب')])
    status_notes = TextAreaField('ملاحظات الحالة', validators=[Optional()])
    phone = StringField('رقم الهاتف', validators=[Optional()])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(message='يرجى إدخال بريد إلكتروني صالح')])
    profile_image = FileField('الصورة الشخصية', validators=[
        FileAllowed(['jpg', 'jpeg', 'png'], 'يسمح فقط بملفات الصور (jpg, jpeg, png)')
    ])
    leave_balance = IntegerField('رصيد الإجازة (بالأيام)', validators=[Optional()])
    hire_date = DateField('تاريخ التعيين', format='%Y-%m-%d', validators=[Optional()])
    last_promotion_date = DateField('تاريخ آخر ترقية', format='%Y-%m-%d', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_military_id(self, military_id):
        employee = Employee.query.filter_by(military_id=military_id.data).first()
        if employee and (not hasattr(self, 'employee_id') or employee.id != self.employee_id):
            raise ValidationError('الرقم العسكري مستخدم بالفعل. يرجى استخدام رقم آخر.')

    def validate_national_id(self, national_id):
        if national_id.data:
            employee = Employee.query.filter_by(national_id=national_id.data).first()
            if employee and (not hasattr(self, 'employee_id') or employee.id != self.employee_id):
                raise ValidationError('الرقم الوطني مستخدم بالفعل. يرجى استخدام رقم آخر.')

class EmployeeSearchForm(FlaskForm):
    search = StringField('بحث', validators=[Optional()])
    status = SelectField('الحالة', choices=[
        ('', 'جميع الحالات'),
        (EmployeeStatus.ACTIVE.name, 'مستمر'),
        (EmployeeStatus.ABSENT.name, 'غائب/هارب'),
        (EmployeeStatus.ASSIGNED.name, 'منتدب'),
        (EmployeeStatus.ON_LEAVE.name, 'في إجازة'),
        (EmployeeStatus.SUSPENDED.name, 'موقوف'),
        (EmployeeStatus.SCATTERED.name, 'متفرق'),
        (EmployeeStatus.MEDICAL.name, 'عيادة طبية')
    ], validators=[Optional()])
    category = SelectField('الفئة', choices=[
        ('', 'جميع الفئات'),
        (EmployeeCategory.OFFICER.name, 'ضباط'),
        (EmployeeCategory.NCO.name, 'ضباط صف'),
        (EmployeeCategory.EMPLOYEE.name, 'موظف')
    ], validators=[Optional()])
    unit = StringField('الوحدة', validators=[Optional()])
    submit = SubmitField('بحث')

class ImportEmployeesForm(FlaskForm):
    file = FileField('ملف Excel', validators=[
        DataRequired(message='هذا الحقل مطلوب'),
        FileAllowed(['xlsx', 'xls'], 'يسمح فقط بملفات Excel (xlsx, xls)')
    ])
    update_existing = BooleanField('تحديث البيانات الموجودة')
    submit = SubmitField('استيراد')
