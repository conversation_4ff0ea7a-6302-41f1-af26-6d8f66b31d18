# دليل إعادة تعيين قاعدة البيانات - نظام إدارة الموارد البشرية

## نظرة عامة

تم تطوير ميزة إعادة تعيين قاعدة البيانات لتوفير طريقة آمنة ومحكمة لحذف جميع البيانات وإنشاء قاعدة بيانات جديدة مع مستخدم مدير افتراضي.

## الميزات المضافة

### 1. زر إعادة تعيين قاعدة البيانات في لوحة التحكم

- **الموقع**: لوحة التحكم الرئيسية (للمديرين فقط)
- **الأمان**: يتطلب صلاحيات المدير
- **التأكيد**: عدة مستويات من التأكيد

### 2. ميزات الأمان

#### أ. التحقق من الصلاحيات
```python
@admin_required  # يتطلب صلاحيات المدير
```

#### ب. التحقق من كلمة المرور
- يجب إدخال كلمة المرور الحالية للمستخدم
- التحقق من صحة كلمة المرور قبل المتابعة

#### ج. التأكيد المتعدد
1. كتابة "RESET_DATABASE" بالضبط
2. نافذة تأكيد نهائية
3. تحذيرات واضحة حول فقدان البيانات

#### د. تسجيل العمليات
- تسجيل جميع محاولات إعادة التعيين
- تسجيل عنوان IP والوقت
- تسجيل المحاولات الفاشلة

### 3. تحسينات الأداء

#### أ. تحسين قاعدة البيانات
```python
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'pool_timeout': 20,
    'max_overflow': 0,
    'echo': False
}
```

#### ب. مراقب الأداء
- مراقبة الاستعلامات البطيئة
- إحصائيات الأداء
- مراقبة استخدام الذاكرة

#### ج. التخزين المؤقت
- تخزين مؤقت للقوالب
- تخزين مؤقت للملفات الثابتة
- تحسين أوقات الاستجابة

### 4. إعدادات الإنتاج

#### أ. ملف الإعدادات المحسن
```python
# config_production.py
class ProductionConfig:
    SECRET_KEY = 'your-super-secret-production-key'
    SQLALCHEMY_ENGINE_OPTIONS = {...}
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
```

#### ب. نص التشغيل المحسن
```bash
# start_production_optimized.bat
python run_production_optimized.py
```

## طريقة الاستخدام

### 1. الوصول إلى ميزة إعادة التعيين

1. سجل الدخول كمدير نظام
2. انتقل إلى لوحة التحكم الرئيسية
3. ابحث عن قسم "إدارة النظام - للمديرين فقط"
4. اضغط على "إعادة تعيين قاعدة البيانات"

### 2. عملية إعادة التعيين

1. **إدخال كلمة المرور**: أدخل كلمة مرورك الحالية
2. **كتابة التأكيد**: اكتب "RESET_DATABASE" بالضبط
3. **التأكيد النهائي**: اضغط على "تأكيد الحذف" في النافذة المنبثقة
4. **انتظار العملية**: ستتم إعادة توجيهك لصفحة تسجيل الدخول

### 3. بعد إعادة التعيين

- **المستخدم الافتراضي الجديد**:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`
  - البريد الإلكتروني: `<EMAIL>`

## التشغيل في الإنتاج

### 1. التشغيل العادي
```bash
cd hrmsystem
start_production_optimized.bat
```

### 2. التشغيل اليدوي
```bash
python run_production_optimized.py
```

### 3. متغيرات البيئة (اختيارية)
```bash
set SECRET_KEY=your-secret-key
set DATABASE_URL=sqlite:///production.db
set HOST=0.0.0.0
set PORT=5000
```

## مراقبة الأداء

### 1. السجلات
- **مجلد السجلات**: `logs/`
- **سجل التشغيل**: `logs/hrm_production.log`
- **سجل البدء**: `logs/startup.log`

### 2. إحصائيات الأداء
```python
# الوصول إلى إحصائيات الأداء
from application.performance_monitor import performance_monitor
report = performance_monitor.get_performance_report()
```

### 3. مراقبة النظام
- استخدام الذاكرة
- استخدام المعالج
- مساحة القرص

## الأمان في الإنتاج

### 1. تغيير كلمة المرور الافتراضية
```python
# بعد أول تسجيل دخول
1. انتقل إلى إعدادات المستخدم
2. غير كلمة المرور من admin123
3. استخدم كلمة مرور قوية
```

### 2. إعدادات الأمان
```python
SESSION_COOKIE_SECURE = True      # HTTPS فقط
SESSION_COOKIE_HTTPONLY = True    # منع JavaScript
WTF_CSRF_ENABLED = True          # حماية CSRF
```

### 3. النسخ الاحتياطية
- يُنصح بعمل نسخة احتياطية قبل إعادة التعيين
- حفظ النسخ الاحتياطية في مكان آمن
- اختبار استعادة النسخ الاحتياطية دورياً

## استكشاف الأخطاء

### 1. مشاكل شائعة

#### أ. خطأ في الصلاحيات
```
الحل: تأكد من تسجيل الدخول كمدير
```

#### ب. خطأ في كلمة المرور
```
الحل: تأكد من إدخال كلمة المرور الصحيحة
```

#### ج. خطأ في قاعدة البيانات
```
الحل: تحقق من ملفات السجلات في مجلد logs/
```

### 2. السجلات المفيدة
```bash
# عرض آخر 50 سطر من السجل
tail -50 logs/hrm_production.log

# البحث عن أخطاء
grep "ERROR" logs/hrm_production.log
```

## الدعم والصيانة

### 1. التحديثات
- تحقق من التحديثات دورياً
- اقرأ ملاحظات الإصدار
- اختبر التحديثات في بيئة التطوير أولاً

### 2. المراقبة الدورية
- راقب استخدام الموارد
- تحقق من السجلات يومياً
- راقب أداء قاعدة البيانات

### 3. النسخ الاحتياطية
- جدولة نسخ احتياطية تلقائية
- اختبار استعادة البيانات
- حفظ النسخ في أماكن متعددة

---

**تحذير مهم**: ميزة إعادة تعيين قاعدة البيانات تحذف جميع البيانات نهائياً. استخدمها بحذر شديد وتأكد من عمل نسخة احتياطية قبل الاستخدام.
