from flask import Flask, redirect, url_for
from flask_login import current_user
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
import os
from datetime import datetime

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
csrf = CSRFProtect()
migrate = Migrate()

def create_app(config_name='default'):
    """إنشاء التطبيق مع إعدادات قابلة للتخصيص"""
    app = Flask(__name__)

    # تحديد الإعدادات حسب البيئة
    if config_name == 'production':
        try:
            from config_production import ProductionConfig
            app.config.from_object(ProductionConfig)
            ProductionConfig.init_app(app)
        except ImportError:
            # استخدام الإعدادات الافتراضية إذا لم تكن إعدادات الإنتاج متوفرة
            from config import Config
            app.config.from_object(Config)
    elif config_name == 'development':
        try:
            from config_production import DevelopmentConfig
            app.config.from_object(DevelopmentConfig)
            DevelopmentConfig.init_app(app)
        except ImportError:
            from config import Config
            app.config.from_object(Config)
    else:
        # الإعدادات الافتراضية
        from config import Config
        app.config.from_object(Config)

    # Ensure the static folder exists
    os.makedirs(os.path.join(app.root_path, 'static'), exist_ok=True)

    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    migrate.init_app(app, db)

    # تهيئة مراقب الأداء في وضع الإنتاج
    if config_name == 'production':
        try:
            from .performance_monitor import performance_monitor
            performance_monitor.init_app(app)
        except ImportError:
            pass  # تجاهل إذا لم يكن مراقب الأداء متوفراً

    # Import blueprints
    from .auth.routes import auth_bp
    from .employees.routes import employees_bp
    from .leaves.routes import leaves_bp
    from .dashboard.routes import dashboard_bp
    from .users.routes import users_bp
    from .reports.routes import reports_bp

    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(employees_bp, url_prefix='/employees')
    app.register_blueprint(leaves_bp, url_prefix='/leaves')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(reports_bp, url_prefix='/reports')

    # Root route
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))

    # Load user callback for Flask-Login
    from .models import User
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Create database tables if they don't exist
    with app.app_context():
        # Create all tables if they don't exist
        db.create_all()

    # Template filters
    @app.template_filter('format_date')
    def format_date(value, format='%Y-%m-%d'):
        if value:
            return value.strftime(format)
        return ""

    # Context processors
    @app.context_processor
    def inject_now():
        return {'now': datetime.now()}

    @app.context_processor
    def inject_permissions():
        from .models import Permission
        return {'Permission': Permission}

    return app
