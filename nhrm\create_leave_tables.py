import sqlite3
from datetime import datetime

def create_leave_tables():
    """إنشاء جداول الإجازات"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # إنشاء جدول أنواع الإجازات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS leave_type (
                id INTEGER NOT NULL PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                color VARCHAR(20),
                created_at DATETIME,
                updated_at DATETIME
            )
        """)
        
        # إنشاء جدول طلبات الإجازات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS leave_request (
                id INTEGER NOT NULL PRIMARY KEY,
                employee_id INTEGER NOT NULL,
                leave_type_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                total_days INTEGER NOT NULL,
                reason TEXT,
                status VARCHAR(20) NOT NULL,
                approved_by INTEGER,
                approved_at DATETIME,
                rejection_reason TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                FOREIGN KEY(employee_id) REFERENCES employee(id),
                FOREIGN KEY(leave_type_id) REFERENCES leave_type(id),
                FOREIGN KEY(approved_by) REFERENCES user(id)
            )
        """)
        
        # إنشاء جدول العطلات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS holiday (
                id INTEGER NOT NULL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                date DATE NOT NULL,
                description TEXT,
                created_at DATETIME,
                updated_at DATETIME
            )
        """)
        
        # إضافة بعض أنواع الإجازات الافتراضية
        leave_types = [
            (1, 'إجازة سنوية', 'إجازة سنوية مدفوعة الأجر', '#28a745', datetime.now().isoformat(), datetime.now().isoformat()),
            (2, 'إجازة مرضية', 'إجازة مرضية مدفوعة الأجر', '#dc3545', datetime.now().isoformat(), datetime.now().isoformat()),
            (3, 'إجازة طارئة', 'إجازة طارئة لظروف خاصة', '#ffc107', datetime.now().isoformat(), datetime.now().isoformat()),
            (4, 'إجازة بدون راتب', 'إجازة غير مدفوعة الأجر', '#6c757d', datetime.now().isoformat(), datetime.now().isoformat())
        ]
        
        # التحقق من وجود أنواع الإجازات قبل إضافتها
        cursor.execute("SELECT id FROM leave_type")
        existing_leave_types = cursor.fetchall()
        existing_ids = [leave_type[0] for leave_type in existing_leave_types]
        
        for leave_type in leave_types:
            if leave_type[0] not in existing_ids:
                cursor.execute("""
                    INSERT INTO leave_type (id, name, description, color, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, leave_type)
        
        conn.commit()
        
        # التحقق من إنشاء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name='leave_type' OR name='leave_request' OR name='holiday')")
        tables = cursor.fetchall()
        
        print("تم إنشاء الجداول التالية:")
        for table in tables:
            print(f"  {table[0]}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء جداول الإجازات: {e}")
        return False

if __name__ == "__main__":
    create_leave_tables()
