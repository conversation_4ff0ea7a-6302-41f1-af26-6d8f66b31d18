from hrmsystem.application import create_app, db
from hrmsystem.application.models import User
import sqlite3

app = create_app()

def add_permissions_column():
    """إضافة عمود permissions إلى جدول user"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'permissions' not in column_names:
            print("إضافة عمود permissions إلى جدول user...")
            cursor.execute("ALTER TABLE user ADD COLUMN permissions TEXT")
            conn.commit()
            print("تم إضافة العمود بنجاح!")
        else:
            print("العمود permissions موجود بالفعل.")
        
        conn.close()
        return True
    except Exception as e:
        print(f"حدث خطأ: {e}")
        return False

if __name__ == "__main__":
    with app.app_context():
        if add_permissions_column():
            # تحديث جميع المستخدمين بقيمة افتراضية للصلاحيات
            users = User.query.all()
            for user in users:
                if user.permissions is None:
                    user.permissions = []
                    db.session.add(user)
            
            db.session.commit()
            print("تم تحديث جميع المستخدمين بنجاح!")
