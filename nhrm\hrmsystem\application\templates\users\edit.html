{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-edit"></i> تعديل بيانات المستخدم</h2>
        <a href="{{ url_for('users.view', id=user.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى بيانات المستخدم
        </a>
    </div>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-edit"></i> بيانات المستخدم</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('users.edit', id=user.id) }}" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <!-- البيانات الأساسية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات الأساسية</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.username.label(class="form-label") }}
                                {% if form.username.errors %}
                                    {{ form.username(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.username(class="form-control") }}
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.email.label(class="form-label") }}
                                {% if form.email.errors %}
                                    {{ form.email(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.email(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- البيانات الشخصية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات الشخصية</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.full_name.label(class="form-label") }}
                                {% if form.full_name.errors %}
                                    {{ form.full_name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.full_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.full_name(class="form-control") }}
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {% if form.phone.errors %}
                                    {{ form.phone(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- الصلاحيات والحالة -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">الصلاحيات والحالة</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.role.label(class="form-label") }}
                                {% if form.role.errors %}
                                    {{ form.role(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.role.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.role(class="form-select") }}
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.status.label(class="form-label") }}
                                {% if form.status.errors %}
                                    {{ form.status(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.status(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- كلمة المرور -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">كلمة المرور</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.password.label(class="form-label") }}
                                {% if form.password.errors %}
                                    {{ form.password(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.password(class="form-control") }}
                                {% endif %}
                                <div class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بكلمة المرور الحالية</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.confirm_password.label(class="form-label") }}
                                {% if form.confirm_password.errors %}
                                    {{ form.confirm_password(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.confirm_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.confirm_password(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- الصورة الشخصية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">الصورة الشخصية</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {% if user.profile_image %}
                                <div class="mb-3">
                                    <p>الصورة الحالية:</p>
                                    <img src="{{ url_for('static', filename='img/users/' + user.profile_image) }}" alt="{{ user.username }}" class="img-thumbnail" style="max-height: 150px;">
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.profile_image.label(class="form-label") }}
                                {% if form.profile_image.errors %}
                                    {{ form.profile_image(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.profile_image.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.profile_image(class="form-control") }}
                                {% endif %}
                                <div class="form-text">يسمح فقط بملفات الصور (jpg, jpeg, png). اترك هذا الحقل فارغًا للاحتفاظ بالصورة الحالية.</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('users.view', id=user.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
