from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash
from datetime import datetime
import os

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hrm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# تعريف نموذج المستخدم
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

# إنشاء مستخدم admin جديد
with app.app_context():
    # إنشاء الجداول إذا لم تكن موجودة
    db.create_all()
    
    # البحث عن المستخدم admin
    admin_user = User.query.filter_by(username="admin").first()
    
    if admin_user:
        # إعادة تعيين كلمة المرور
        admin_user.password = generate_password_hash("admin123")
        db.session.commit()
        print("=" * 50)
        print("تم إعادة تعيين كلمة المرور بنجاح!")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور: admin123")
        print("=" * 50)
    else:
        # إنشاء مستخدم admin جديد
        print("جاري إنشاء مستخدم admin جديد...")
        new_admin = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),
            is_admin=True
        )
        db.session.add(new_admin)
        db.session.commit()
        
        print("=" * 50)
        print("تم إنشاء مستخدم admin جديد بنجاح!")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور: admin123")
        print("=" * 50)

    # التحقق من المستخدمين الموجودين
    users = User.query.all()
    print("\nالمستخدمون الموجودون في قاعدة البيانات:")
    for user in users:
        print(f"ID: {user.id}, اسم المستخدم: {user.username}, البريد الإلكتروني: {user.email}")
