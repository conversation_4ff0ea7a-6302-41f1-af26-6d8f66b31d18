from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
from sqlalchemy import Column, Date

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# تعريف نموذج الموظف
class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    military_id = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    hire_date = db.Column(db.Date, nullable=True)
    last_promotion_date = db.Column(db.Date, nullable=True)

# تحديث قاعدة البيانات
with app.app_context():
    # إنشاء الجداول إذا لم تكن موجودة
    db.create_all()
    print("تم إنشاء الجداول بنجاح")

    # التحقق من وجود الأعمدة الجديدة
    inspector = db.inspect(db.engine)
    columns = inspector.get_columns('employee')
    column_names = [column['name'] for column in columns]

    print(f"الأعمدة الموجودة في جدول employee: {column_names}")

    if 'hire_date' in column_names:
        print("عمود hire_date موجود بالفعل")
    else:
        print("عمود hire_date غير موجود")

    if 'last_promotion_date' in column_names:
        print("عمود last_promotion_date موجود بالفعل")
    else:
        print("عمود last_promotion_date غير موجود")

    print("تم تحديث قاعدة البيانات بنجاح")
