{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-list-alt"></i> أنواع الإجازات</h2>
        <div>
            <a href="{{ url_for('leaves.create_type') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة نوع إجازة جديد
            </a>
            <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى الإجازات
            </a>
        </div>
    </div>

    <!-- Leave Types List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> قائمة أنواع الإجازات</h5>
        </div>
        <div class="card-body">
            {% if leave_types %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>اللون</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave_type in leave_types %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ leave_type.name }}</td>
                            <td>{{ leave_type.description }}</td>
                            <td>
                                <span class="badge" style="background-color: {{ leave_type.color }}">{{ leave_type.color }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('leaves.edit_type', id=leave_type.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ leave_type.id }}" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ leave_type.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ leave_type.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ leave_type.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف نوع الإجازة <strong>{{ leave_type.name }}</strong>؟
                                                <div class="alert alert-warning mt-3">
                                                    <i class="fas fa-exclamation-triangle"></i> تحذير: لا يمكن حذف نوع الإجازة إذا كان مستخدمًا في أي طلب إجازة.
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('leaves.delete_type', id=leave_type.id) }}" method="POST">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا توجد أنواع إجازات مسجلة.</p>
                <a href="{{ url_for('leaves.create_type') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> إضافة نوع إجازة جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
