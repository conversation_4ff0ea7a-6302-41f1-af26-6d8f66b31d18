#!/usr/bin/env python3
"""
نص تشغيل محسن للإنتاج - نظام إدارة الموارد البشرية
Production Optimized Startup Script - HR Management System

هذا النص يوفر:
- تحسينات الأداء
- إعدادات الأمان
- تسجيل السجلات المحسن
- إدارة الأخطاء
- مراقبة الصحة
"""

import os
import sys
import logging
import signal
import time
from datetime import datetime
from werkzeug.middleware.profiler import ProfilerMiddleware
from werkzeug.serving import WSGIRequestHandler

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from application import create_app
from config_production import ProductionConfig

# إعداد التسجيل المبكر
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/startup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class OptimizedRequestHandler(WSGIRequestHandler):
    """معالج طلبات محسن للأداء"""
    
    def log_request(self, code='-', size='-'):
        """تسجيل مخصص للطلبات"""
        if code != 200:  # تسجيل الأخطاء فقط
            super().log_request(code, size)

def setup_signal_handlers(app):
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    
    def signal_handler(signum, frame):
        logger.info(f"تم استلام إشارة {signum}. بدء الإغلاق الآمن...")
        
        # إغلاق اتصالات قاعدة البيانات
        try:
            from application import db
            db.session.close()
            db.engine.dispose()
            logger.info("تم إغلاق اتصالات قاعدة البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في إغلاق قاعدة البيانات: {e}")
        
        logger.info("تم إيقاف التطبيق بنجاح")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'logs',
        'application/static/uploads',
        'application/static/uploads/employees',
        'application/static/uploads/profile_pics',
        'backups'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            logger.info(f"تم إنشاء المجلد: {directory}")

def check_database_health(app):
    """فحص صحة قاعدة البيانات"""
    try:
        with app.app_context():
            from application import db
            from application.models import User
            
            # اختبار الاتصال
            db.session.execute('SELECT 1')
            
            # التحقق من وجود المدير
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                logger.warning("لم يتم العثور على مستخدم المدير الافتراضي")
            
            logger.info("فحص قاعدة البيانات: ✓ سليمة")
            return True
            
    except Exception as e:
        logger.error(f"خطأ في فحص قاعدة البيانات: {e}")
        return False

def optimize_app_settings(app):
    """تحسين إعدادات التطبيق للإنتاج"""
    
    # تعطيل وضع التطوير
    app.config['DEBUG'] = False
    app.config['TESTING'] = False
    
    # تحسين إعدادات Flask
    app.config['PROPAGATE_EXCEPTIONS'] = True
    
    # إعدادات الأمان
    app.config['SESSION_COOKIE_SECURE'] = True
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    # تحسين إعدادات قاعدة البيانات
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0,
        'echo': False
    }
    
    logger.info("تم تطبيق تحسينات الإنتاج")

def setup_error_handlers(app):
    """إعداد معالجات الأخطاء المخصصة"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        logger.warning(f"صفحة غير موجودة: {request.url}")
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"خطأ داخلي: {error}")
        from application import db
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        logger.warning(f"وصول محظور: {request.url}")
        return render_template('errors/403.html'), 403

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    logger.info("=" * 60)
    logger.info("بدء تشغيل نظام إدارة الموارد البشرية - وضع الإنتاج")
    logger.info("=" * 60)
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    
    # إنشاء التطبيق
    try:
        app = create_app('production')
        logger.info("تم إنشاء التطبيق بنجاح")
    except Exception as e:
        logger.error(f"فشل في إنشاء التطبيق: {e}")
        sys.exit(1)
    
    # تطبيق التحسينات
    optimize_app_settings(app)
    
    # إعداد معالجات الإشارات
    setup_signal_handlers(app)
    
    # إعداد معالجات الأخطاء
    setup_error_handlers(app)
    
    # فحص صحة قاعدة البيانات
    if not check_database_health(app):
        logger.error("فشل في فحص قاعدة البيانات. توقف التشغيل.")
        sys.exit(1)
    
    # إعدادات الخادم
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    
    # إعدادات SSL (إذا كانت متوفرة)
    ssl_context = None
    if os.path.exists('ssl/cert.pem') and os.path.exists('ssl/key.pem'):
        ssl_context = ('ssl/cert.pem', 'ssl/key.pem')
        logger.info("تم تفعيل SSL")
    
    logger.info(f"بدء الخادم على {host}:{port}")
    logger.info(f"وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # تشغيل الخادم
        app.run(
            host=host,
            port=port,
            debug=False,
            threaded=True,
            ssl_context=ssl_context,
            request_handler=OptimizedRequestHandler
        )
    except KeyboardInterrupt:
        logger.info("تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
