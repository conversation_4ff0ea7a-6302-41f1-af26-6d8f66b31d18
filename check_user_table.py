import sqlite3

def check_user_table():
    """التحقق من هيكل جدول user"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # عرض هيكل الجدول
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        
        print("هيكل جدول user:")
        for column in columns:
            print(f"  {column[1]} ({column[2]})")
        
        # عرض عدد المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        count = cursor.fetchone()[0]
        print(f"\nعدد المستخدمين: {count}")
        
        # عرض بيانات المستخدمين
        if count > 0:
            cursor.execute("SELECT * FROM user LIMIT 1")
            user = cursor.fetchone()
            column_names = [description[0] for description in cursor.description]
            
            print("\nبيانات المستخدم الأول:")
            for i, value in enumerate(user):
                print(f"  {column_names[i]}: {value}")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_user_table()
