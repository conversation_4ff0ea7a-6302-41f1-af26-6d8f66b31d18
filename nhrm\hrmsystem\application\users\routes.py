from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from ..models import User, UserRole, UserStatus, UserAuditLog, AuditLog, Permission
from .. import db
from .forms import UserForm, UserSearchForm, ChangePasswordForm, ProfileForm, PermissionsForm
from ..utilities import save_picture
from ..decorators import admin_required, permission_required
from flask import current_app
import os
import json
from datetime import datetime
from werkzeug.security import generate_password_hash

users_bp = Blueprint('users', __name__)

@users_bp.route('/')
@login_required
def index():
    # Check if user has admin privileges
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    form = UserSearchForm()
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('USERS_PER_PAGE', 10)

    # Get search parameters from request args
    search = request.args.get('search', '')
    role = request.args.get('role', '')
    status = request.args.get('status', '')

    # Build query
    query = User.query

    if search:
        query = query.filter(
            (User.username.ilike(f'%{search}%')) |
            (User.email.ilike(f'%{search}%')) |
            (User.full_name.ilike(f'%{search}%'))
        )

    if role:
        query = query.filter(User.role == role)

    if status:
        query = query.filter(User.status == status)

    # Order by username
    query = query.order_by(User.username)

    # Paginate results
    users = query.paginate(page=page, per_page=per_page)

    return render_template('users/index.html',
                          users=users,
                          form=form,
                          search=search,
                          role=role,
                          status=status,
                          title='إدارة المستخدمين')

@users_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has admin privileges
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    form = UserForm()

    if form.validate_on_submit():
        # Handle profile image
        profile_image = None
        if form.profile_image.data:
            profile_image = save_picture(form.profile_image.data, folder='users')

        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            phone=form.phone.data,
            role=form.role.data,
            status=form.status.data,
            profile_image=profile_image,
            is_admin=True if form.role.data == UserRole.ADMIN.name else False
        )

        # Set password if provided
        if form.password.data:
            user.set_password(form.password.data)
        else:
            # Set default password
            user.set_password('password123')

        # إضافة المستخدم إلى قاعدة البيانات والحصول على ID
        db.session.add(user)
        db.session.flush()  # للتأكد من أن user.id له قيمة

        # التأكد من أن user.id له قيمة
        if user.id is None:
            db.session.commit()  # إذا لم يكن له قيمة، قم بالتنفيذ

        print(f"User ID after flush: {user.id}")  # للتشخيص

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='User',
            entity_id=user.id,
            details=f'تم إنشاء المستخدم {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        # التأكد من أن user.id له قيمة صالحة
        user_id_for_log = user.id if user.id is not None else 1
        action_by_id = current_user.id if current_user.id is not None else 1

        # Create user audit log
        try:
            user_log = UserAuditLog(
                user_id=user_id_for_log,
                action_by=action_by_id,
                action='create',
                new_values=json.dumps({
                    'username': user.username,
                    'email': user.email,
                    'full_name': user.full_name,
                    'phone': user.phone,
                    'role': form.role.data,
                    'status': form.status.data,
                    'is_admin': user.is_admin
                }, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
            print(f"UserAuditLog created with user_id: {user_id_for_log}, action_by: {action_by_id}")  # للتشخيص
        except Exception as e:
            # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
            print(f"Error creating UserAuditLog: {e}")
            user_log = UserAuditLog(
                user_id=1,  # قيمة افتراضية
                action_by=1,  # قيمة افتراضية
                action='create',
                new_values=json.dumps({
                    'username': user.username,
                    'email': user.email,
                    'full_name': user.full_name,
                    'phone': user.phone,
                    'role': form.role.data,
                    'status': form.status.data,
                    'is_admin': user.is_admin
                }, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        db.session.add(user_log)

        db.session.commit()
        flash(f'تم إنشاء المستخدم {form.username.data} بنجاح!', 'success')
        return redirect(url_for('users.index'))

    return render_template('users/create.html', form=form, title='إضافة مستخدم جديد')

@users_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Check if user has admin privileges
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(id)
    form = UserForm(obj=user)
    form.user_id = id

    if form.validate_on_submit():
        # Store old values for audit log
        old_values = {
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'phone': user.phone,
            'role': user.role.name if user.role else None,
            'status': user.status.name if user.status else None,
            'is_admin': user.is_admin
        }

        # Handle profile image
        if form.profile_image.data and hasattr(form.profile_image.data, 'filename'):
            profile_image = save_picture(form.profile_image.data, folder='users')
            user.profile_image = profile_image

        # Update user data
        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.phone = form.phone.data
        user.role = form.role.data
        user.status = form.status.data
        user.is_admin = True if form.role.data == UserRole.ADMIN.name else False

        # Update password if provided
        if form.password.data:
            user.set_password(form.password.data)

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='User',
            entity_id=user.id,
            details=f'تم تحديث بيانات المستخدم {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        # Create user audit log
        new_values = {
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'phone': user.phone,
            'role': form.role.data,
            'status': form.status.data,
            'is_admin': user.is_admin
        }

        try:
            user_log = UserAuditLog(
                user_id=user.id,
                action_by=current_user.id,
                action='update',
                old_values=json.dumps(old_values, ensure_ascii=False),
                new_values=json.dumps(new_values, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        except Exception as e:
            # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
            print(f"Error creating UserAuditLog: {e}")
            user_log = UserAuditLog(
                user_id=1,  # قيمة افتراضية
                action_by=1,  # قيمة افتراضية
                action='update',
                old_values=json.dumps(old_values, ensure_ascii=False),
                new_values=json.dumps(new_values, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        db.session.add(user_log)

        db.session.commit()
        flash(f'تم تحديث بيانات المستخدم {form.username.data} بنجاح!', 'success')
        return redirect(url_for('users.view', id=user.id))

    return render_template('users/edit.html', form=form, user=user, title='تعديل بيانات مستخدم')

@users_bp.route('/view/<int:id>')
@login_required
def view(id):
    # Check if user has admin privileges or is viewing their own profile
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN and current_user.id != id:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(id)

    # Get user audit logs
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('AUDIT_LOGS_PER_PAGE', 10)

    audit_logs = UserAuditLog.query.filter_by(user_id=user.id).order_by(UserAuditLog.timestamp.desc()).paginate(page=page, per_page=per_page)

    return render_template('users/view.html', user=user, audit_logs=audit_logs, title=f'بيانات المستخدم: {user.username}')

@users_bp.route('/permissions/<int:id>', methods=['GET', 'POST'])
@login_required
def manage_permissions(id):
    # التحقق من صلاحيات المستخدم
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN and not current_user.has_permission(Permission.MANAGE_PERMISSIONS):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(id)

    # لا يمكن تعديل صلاحيات مدير النظام
    if user.is_admin or user.role == 'admin' or user.role == UserRole.ADMIN:
        flash('لا يمكن تعديل صلاحيات مدير النظام', 'warning')
        return redirect(url_for('users.view', id=user.id))

    form = PermissionsForm()

    # عند تقديم النموذج
    if form.validate_on_submit():
        # تخزين القيم القديمة لسجل التدقيق
        import json
        try:
            old_permissions = json.loads(user.permissions) if user.permissions else []
        except:
            old_permissions = []

        # جمع جميع الصلاحيات المحددة
        selected_permissions = []
        selected_permissions.extend(form.employee_permissions.data)
        selected_permissions.extend(form.leave_permissions.data)
        selected_permissions.extend(form.user_permissions.data)
        selected_permissions.extend(form.report_permissions.data)
        selected_permissions.extend(form.system_permissions.data)

        # تحويل الأسماء إلى كائنات Permission
        permissions = [getattr(Permission, p) for p in selected_permissions]

        # تعيين الصلاحيات للمستخدم
        user.set_permissions(permissions)

        # إنشاء سجل تدقيق
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='User',
            entity_id=user.id,
            details=f'تم تحديث صلاحيات المستخدم {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        # إنشاء سجل تدقيق المستخدم
        user_log = UserAuditLog(
            user_id=user.id,
            action_by=current_user.id,
            action='permissions_update',
            old_values=json.dumps({'permissions': old_permissions}, ensure_ascii=False),
            new_values=json.dumps({'permissions': selected_permissions}, ensure_ascii=False),
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.add(user_log)

        db.session.commit()
        flash(f'تم تحديث صلاحيات المستخدم {user.username} بنجاح!', 'success')
        return redirect(url_for('users.view', id=user.id))

    # عند فتح الصفحة
    elif request.method == 'GET':
        # تعبئة النموذج بالصلاحيات الحالية
        import json
        try:
            current_permissions = json.loads(user.permissions) if user.permissions else []
        except:
            current_permissions = []

        # تعيين القيم المحددة لكل مجموعة صلاحيات
        form.employee_permissions.data = [p for p in current_permissions if p.startswith('VIEW_EMPLOYEES') or p.startswith('ADD_EMPLOYEE') or p.startswith('EDIT_EMPLOYEE') or p.startswith('DELETE_EMPLOYEE') or p.startswith('EXPORT_EMPLOYEES') or p.startswith('IMPORT_EMPLOYEES') or p.startswith('PRINT_EMPLOYEE')]
        form.leave_permissions.data = [p for p in current_permissions if p.startswith('VIEW_LEAVES') or p.startswith('ADD_LEAVE') or p.startswith('EDIT_LEAVE') or p.startswith('DELETE_LEAVE') or p.startswith('APPROVE_LEAVE') or p.startswith('REJECT_LEAVE')]
        form.user_permissions.data = [p for p in current_permissions if p.startswith('VIEW_USERS') or p.startswith('ADD_USER') or p.startswith('EDIT_USER') or p.startswith('DELETE_USER') or p.startswith('MANAGE_PERMISSIONS')]
        form.report_permissions.data = [p for p in current_permissions if p.startswith('VIEW_REPORTS') or p.startswith('EXPORT_REPORTS') or p.startswith('PRINT_REPORTS')]
        form.system_permissions.data = [p for p in current_permissions if p.startswith('VIEW_AUDIT_LOGS') or p.startswith('MANAGE_SYSTEM')]

    return render_template('users/permissions.html', form=form, user=user, title=f'إدارة صلاحيات المستخدم: {user.username}')

@users_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    # Check if user has admin privileges
    if not current_user.is_admin and current_user.role != 'admin' and current_user.role != UserRole.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(id)

    # Prevent deleting self
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص!', 'danger')
        return redirect(url_for('users.index'))

    # Store user data for audit log
    user_data = {
        'username': user.username,
        'email': user.email,
        'full_name': user.full_name,
        'role': user.role.name if user.role else None,
        'status': user.status.name if user.status else None
    }

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='User',
        entity_id=user.id,
        details=f'تم حذف المستخدم {user.username}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    # Create user audit log
    try:
        user_log = UserAuditLog(
            user_id=user.id,
            action_by=current_user.id,
            action='delete',
            old_values=json.dumps(user_data, ensure_ascii=False),
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
    except Exception as e:
        # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
        print(f"Error creating UserAuditLog: {e}")
        user_log = UserAuditLog(
            user_id=1,  # قيمة افتراضية
            action_by=1,  # قيمة افتراضية
            action='delete',
            old_values=json.dumps(user_data, ensure_ascii=False),
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
    db.session.add(user_log)

    db.session.delete(user)
    db.session.commit()
    flash(f'تم حذف المستخدم {user.username} بنجاح!', 'success')
    return redirect(url_for('users.index'))

@users_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    form = ProfileForm(obj=current_user)

    if form.validate_on_submit():
        # Store old values for audit log
        old_values = {
            'email': current_user.email,
            'full_name': current_user.full_name,
            'phone': current_user.phone
        }

        # Handle profile image
        if form.profile_image.data and hasattr(form.profile_image.data, 'filename'):
            profile_image = save_picture(form.profile_image.data, folder='users')
            current_user.profile_image = profile_image

        # Update user data
        current_user.email = form.email.data
        current_user.full_name = form.full_name.data
        current_user.phone = form.phone.data

        # Create user audit log
        new_values = {
            'email': current_user.email,
            'full_name': current_user.full_name,
            'phone': current_user.phone
        }

        try:
            user_log = UserAuditLog(
                user_id=current_user.id,
                action_by=current_user.id,
                action='profile_update',
                old_values=json.dumps(old_values, ensure_ascii=False),
                new_values=json.dumps(new_values, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        except Exception as e:
            # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
            print(f"Error creating UserAuditLog: {e}")
            user_log = UserAuditLog(
                user_id=1,  # قيمة افتراضية
                action_by=1,  # قيمة افتراضية
                action='profile_update',
                old_values=json.dumps(old_values, ensure_ascii=False),
                new_values=json.dumps(new_values, ensure_ascii=False),
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        db.session.add(user_log)

        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح!', 'success')
        return redirect(url_for('users.profile'))

    return render_template('users/profile.html', form=form, title='الملف الشخصي')

@users_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('كلمة المرور الحالية غير صحيحة!', 'danger')
            return redirect(url_for('users.change_password'))

        current_user.set_password(form.new_password.data)

        # Create user audit log
        try:
            user_log = UserAuditLog(
                user_id=current_user.id,
                action_by=current_user.id,
                action='password_change',
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        except Exception as e:
            # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
            print(f"Error creating UserAuditLog: {e}")
            user_log = UserAuditLog(
                user_id=1,  # قيمة افتراضية
                action_by=1,  # قيمة افتراضية
                action='password_change',
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
        db.session.add(user_log)

        db.session.commit()
        flash('تم تغيير كلمة المرور بنجاح!', 'success')
        return redirect(url_for('users.profile'))

    return render_template('users/change_password.html', form=form, title='تغيير كلمة المرور')

@users_bp.route('/audit-logs')
@login_required
def audit_logs():
    # Check if user has admin privileges
    if not current_user.is_admin and current_user.role != UserRole.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('AUDIT_LOGS_PER_PAGE', 20)

    # Get search parameters
    user_id = request.args.get('user_id', type=int)
    action = request.args.get('action', '')

    # Build query
    query = UserAuditLog.query

    if user_id:
        query = query.filter_by(user_id=user_id)

    if action:
        query = query.filter_by(action=action)

    # Order by timestamp
    query = query.order_by(UserAuditLog.timestamp.desc())

    # Paginate results
    logs = query.paginate(page=page, per_page=per_page)

    # Get all users for filter dropdown
    users = User.query.order_by(User.username).all()

    # Get all actions for filter dropdown
    actions = db.session.query(UserAuditLog.action).distinct().all()
    actions = [action[0] for action in actions]

    return render_template('users/audit_logs.html',
                          logs=logs,
                          users=users,
                          actions=actions,
                          user_id=user_id,
                          action=action,
                          title='سجل تغييرات المستخدمين')
