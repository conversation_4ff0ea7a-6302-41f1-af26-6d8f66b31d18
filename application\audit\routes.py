from flask import render_template, request, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import desc

from . import audit_bp
from .. import db
from ..models import AuditLog, User

@audit_bp.route('/')
@login_required
def index():
    # Check if user has admin role
    if not current_user.is_admin:
        return redirect(url_for('dashboard.index'))
    
    # Get filter parameters
    user_id = request.args.get('user_id', type=int)
    action = request.args.get('action')
    entity_type = request.args.get('entity_type')
    date_range = request.args.get('date_range')
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = AuditLog.query
    
    # Apply filters
    if user_id:
        query = query.filter(AuditLog.user_id == user_id)
    
    if action:
        query = query.filter(AuditLog.action == action)
    
    if entity_type:
        query = query.filter(AuditLog.entity_type == entity_type)
    
    # Apply date range filter
    if date_range:
        today = datetime.now().date()
        if date_range == 'today':
            start_date = datetime.combine(today, datetime.min.time())
            end_date = datetime.combine(today, datetime.max.time())
        elif date_range == 'yesterday':
            yesterday = today - timedelta(days=1)
            start_date = datetime.combine(yesterday, datetime.min.time())
            end_date = datetime.combine(yesterday, datetime.max.time())
        elif date_range == 'week':
            # Get the start of the week (Sunday)
            start_of_week = today - timedelta(days=today.weekday() + 1)
            if start_of_week.weekday() == 6:  # If today is Sunday
                start_of_week = today
            start_date = datetime.combine(start_of_week, datetime.min.time())
            end_date = datetime.combine(today, datetime.max.time())
        elif date_range == 'month':
            # Get the start of the month
            start_of_month = today.replace(day=1)
            start_date = datetime.combine(start_of_month, datetime.min.time())
            end_date = datetime.combine(today, datetime.max.time())
        
        query = query.filter(AuditLog.timestamp >= start_date, AuditLog.timestamp <= end_date)
    
    # Order by timestamp descending
    query = query.order_by(desc(AuditLog.timestamp))
    
    # Paginate results
    logs = query.paginate(page=page, per_page=20, error_out=False)
    
    # Get all users for filter dropdown
    users = User.query.all()
    
    return render_template('audit/index.html', logs=logs, users=users)
