<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} | نظام إدارة الموارد البشرية</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Print CSS -->
    <style>
        @font-face {
            font-family: 'Times New Roman';
            src: url('{{ url_for("static", filename="fonts/times.ttf") }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Times New Roman';
            src: url('{{ url_for("static", filename="fonts/timesbd.ttf") }}') format('truetype');
            font-weight: bold;
            font-style: normal;
        }

        @page {
            size: A4 portrait;
            margin: 1cm 1cm 1.5cm 1cm; /* top right bottom left */
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 14pt;
            line-height: 1.5;
            color: #000;
            background-color: #fff;
            padding: 0;
            margin: 0;
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 15px;
        }

        .report-title {
            font-size: 20pt;
            font-weight: bold;
            margin-bottom: 10px;
            color: #0056b3;
        }

        .report-subtitle {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 8px;
            background-color: #e3f2fd;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #0056b3;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            table-layout: fixed; /* لضمان عدم تجاوز عرض الجدول لحدود الصفحة */
        }

        .report-table th, .report-table td {
            border: 1.5px solid #0056b3;
            padding: 8px 5px; /* تقليل التباعد الداخلي لتوفير مساحة أكبر */
            text-align: center;
            word-wrap: break-word; /* للسماح بالتفاف النص الطويل */
            overflow: hidden;
        }

        .report-table th {
            background-color: #b8d1f3;
            font-weight: bold;
            font-size: 13pt; /* تقليل حجم الخط قليلاً للعناوين */
            color: #0056b3;
        }

        .report-table td {
            font-size: 14pt;
        }

        .report-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .report-table tr:hover {
            background-color: #e3f2fd;
        }

        .report-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px; /* تقليل المسافة قليلاً */
            border-top: 1px solid #0056b3;
            padding-top: 10px;
            position: relative; /* لضمان عدم تجاوز حدود الصفحة */
            bottom: 0;
        }

        .page-number {
            text-align: center;
            margin-top: 15px;
            font-size: 11pt;
            color: #0056b3;
            position: relative;
        }

        /* تخصيص الألوان حسب نوع التقرير */
        .report-rank th {
            background-color: #b8d1f3; /* أزرق فاتح */
            color: #0056b3;
        }

        .report-unit th {
            background-color: #c6e0b4; /* أخضر فاتح */
            color: #2e7d32;
        }

        .report-category th {
            background-color: #f8cbad; /* برتقالي فاتح */
            color: #e65100;
        }

        .report-status th {
            background-color: #ffe699; /* أصفر فاتح */
            color: #ff8f00;
        }

        .report-leave th {
            background-color: #d9d2e9; /* بنفسجي فاتح */
            color: #6a1b9a;
        }

        /* الترقيم المتسلسل */
        .serial-number {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        /* أزرار التحكم */
        .control-buttons {
            margin-bottom: 20px;
        }

        .btn-print, .btn-back, .btn-export {
            padding: 8px 20px;
            font-size: 14pt;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn-print {
            background-color: #0056b3;
            color: white;
            border: none;
        }

        .btn-print:hover {
            background-color: #003d82;
        }

        .btn-back {
            background-color: #6c757d;
            color: white;
            border: none;
        }

        .btn-back:hover {
            background-color: #5a6268;
        }

        .btn-export {
            background-color: #28a745;
            color: white;
            border: none;
        }

        .btn-export:hover {
            background-color: #218838;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            a {
                text-decoration: none;
                color: #000;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
                box-sizing: border-box;
            }

            body {
                font-size: 14pt;
                line-height: 1.4; /* تقليل ارتفاع السطر قليلاً لتوفير مساحة */
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .report-table th, .report-table td {
                font-size: 13pt; /* تقليل حجم الخط قليلاً عند الطباعة */
                padding: 6px 4px; /* تقليل التباعد الداخلي عند الطباعة */
            }

            /* ضمان ظهور الألوان عند الطباعة */
            .report-table th {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .report-rank th, .report-unit th, .report-category th, .report-status th, .report-leave th {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>

    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container" style="width: 21cm; max-width: 100%; margin: 0 auto; box-sizing: border-box;">
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Print Script -->
    <script>
        $(document).ready(function() {
            // إذا كان هناك زر طباعة، قم بإضافة وظيفة الطباعة له
            $('.btn-print').click(function() {
                window.print();
                return false;
            });

            // إذا كان هناك زر رجوع، قم بإضافة وظيفة الرجوع له
            $('.btn-back').click(function() {
                window.history.back();
                return false;
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
