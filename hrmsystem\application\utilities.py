import os
import secrets
import sqlite3
from PIL import Image
from flask import current_app
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash
from .models import Holiday
from . import db
import qrcode
import io
import base64
import calendar

def save_picture(form_picture, folder='profile_pics', size=(150, 150)):
    """Save a picture to the filesystem and return the filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(current_app.root_path, 'static/img', folder, picture_fn)

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)

    # Resize image
    i = Image.open(form_picture)
    i.thumbnail(size)
    i.save(picture_path)

    return picture_fn

def calculate_leave_days(start_date, end_date, exclude_weekends=True, exclude_holidays=True):
    """Calculate the number of days between two dates, excluding weekends and holidays if specified"""
    if start_date > end_date:
        return 0

    # Get all dates between start_date and end_date
    delta = end_date - start_date
    all_days = [start_date + timedelta(days=i) for i in range(delta.days + 1)]

    # Get weekend days from config
    weekend_days = current_app.config.get('WEEKEND_DAYS', [4, 5])  # Default: Friday and Saturday

    # Filter out weekend days if needed
    if exclude_weekends:
        all_days = [day for day in all_days if day.weekday() not in weekend_days]

    # Filter out holidays if needed
    if exclude_holidays:
        try:
            holidays = Holiday.query.filter(
                Holiday.date.between(start_date, end_date)
            ).all()
            holiday_dates = [holiday.date for holiday in holidays]
            all_days = [day for day in all_days if day not in holiday_dates]
        except Exception:
            # If there's an error (e.g., Holiday table doesn't exist yet), just continue
            pass

    return len(all_days)

def generate_qr_code(data, size=200):
    """Generate a QR code for the given data"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # Resize the image to the requested size
    img = img.resize((size, size))

    # Convert to base64 for embedding in HTML
    buffered = io.BytesIO()
    img.save(buffered)
    img_str = base64.b64encode(buffered.getvalue()).decode()

    return f"data:image/png;base64,{img_str}"

def generate_employee_qr_code(employee, size=200):
    """Generate a QR code with employee data"""
    # Create a string with employee data
    employee_data = f"الاسم: {employee.name}\n"
    employee_data += f"الرقم العسكري: {employee.military_id}\n"

    if employee.national_id:
        employee_data += f"الرقم الوطني: {employee.national_id}\n"

    if employee.military_rank:
        employee_data += f"الرتبة: {employee.military_rank}\n"

    if employee.unit:
        employee_data += f"الوحدة: {employee.unit}\n"

    if employee.position:
        employee_data += f"العمل المكلف به: {employee.position}\n"

    if employee.category:
        employee_data += f"الفئة: {employee.category.value}\n"

    if employee.status:
        employee_data += f"الحالة: {employee.status.value}\n"

    if employee.phone:
        employee_data += f"رقم الهاتف: {employee.phone}\n"

    # Generate QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,  # Higher error correction for more data
        box_size=10,
        border=4,
    )
    qr.add_data(employee_data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # Resize the image to the requested size
    img = img.resize((size, size))

    # Convert to base64 for embedding in HTML
    buffered = io.BytesIO()
    img.save(buffered)
    img_str = base64.b64encode(buffered.getvalue()).decode()

    return f"data:image/png;base64,{img_str}"

def format_date(date, format='%Y-%m-%d'):
    """Format a date object to string"""
    if date:
        return date.strftime(format)
    return ""

def parse_date(date_str, format='%Y-%m-%d'):
    """Parse a date string to date object"""
    if date_str:
        try:
            return datetime.strptime(date_str, format).date()
        except ValueError:
            return None
    return None

def get_employee_status_color(status):
    """Get color for employee status"""
    colors = {
        'مستمر': 'success',
        'غائب/هارب': 'danger',
        'منتدب': 'info',
        'في إجازة': 'warning',
        'موقوف': 'secondary',
        'متفرق': 'primary',
        'عيادة طبية': 'dark'
    }
    return colors.get(status.value, 'secondary')

def get_leave_status_color(status):
    """Get color for leave status"""
    colors = {
        'قيد الانتظار': 'warning',
        'موافق عليها': 'success',
        'مرفوضة': 'danger',
        'ملغية': 'secondary'
    }
    return colors.get(status.value, 'secondary')

def reset_database_secure():
    """إعادة تعيين قاعدة البيانات بشكل آمن مع إنشاء مستخدم مدير افتراضي"""
    try:
        # الحصول على مسار قاعدة البيانات
        db_path = current_app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
        if not db_path:
            # استخدام المسار الافتراضي
            db_path = os.path.join(current_app.instance_path, 'hrm.db')

        # إغلاق جميع الاتصالات الحالية
        db.session.close()
        db.engine.dispose()

        # حذف قاعدة البيانات الحالية إذا كانت موجودة
        if os.path.exists(db_path):
            os.remove(db_path)
            print(f"تم حذف قاعدة البيانات: {db_path}")

        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE user (
                id INTEGER NOT NULL PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(150) NOT NULL UNIQUE,
                password VARCHAR(150) NOT NULL,
                role VARCHAR(50) DEFAULT 'USER',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                full_name TEXT,
                status VARCHAR(50) DEFAULT 'ACTIVE',
                phone TEXT,
                profile_image TEXT,
                is_admin BOOLEAN DEFAULT 0
            )
        """)

        # إنشاء الفهارس للمستخدمين
        cursor.execute("CREATE UNIQUE INDEX ix_user_username ON user (username)")
        cursor.execute("CREATE UNIQUE INDEX ix_user_email ON user (email)")

        # إنشاء مستخدم مدير افتراضي
        admin_password = generate_password_hash('admin123')
        now = datetime.now().isoformat()

        cursor.execute("""
            INSERT INTO user (username, email, password, role, created_at, full_name, status, is_admin)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'admin',
            '<EMAIL>',
            admin_password,
            'ADMIN',
            now,
            'مدير النظام',
            'ACTIVE',
            1
        ))

        # إنشاء جدول الموظفين
        cursor.execute("""
            CREATE TABLE employee (
                id INTEGER NOT NULL PRIMARY KEY,
                military_id VARCHAR(20) NOT NULL UNIQUE,
                name VARCHAR(100) NOT NULL,
                national_id VARCHAR(20),
                blood_type VARCHAR(10),
                date_of_birth DATE,
                birth_place VARCHAR(100),
                current_residence VARCHAR(200),
                education VARCHAR(100),
                education_date DATE,
                category VARCHAR(50),
                bank_name VARCHAR(100),
                bank_account VARCHAR(50),
                military_rank VARCHAR(50),
                unit VARCHAR(100),
                position VARCHAR(100),
                status VARCHAR(50),
                status_notes TEXT,
                phone VARCHAR(20),
                email VARCHAR(120),
                profile_image VARCHAR(255),
                leave_balance INTEGER DEFAULT 30,
                hire_date DATE,
                last_promotion_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # إنشاء جدول أنواع الإجازات
        cursor.execute("""
            CREATE TABLE leave_type (
                id INTEGER NOT NULL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                max_days INTEGER,
                requires_approval BOOLEAN DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # إدراج أنواع الإجازات الافتراضية
        leave_types = [
            ('إجازة اعتيادية', 30, 1, 1),
            ('إجازة مرضية', 15, 1, 1),
            ('إجازة طارئة', 3, 1, 1),
            ('إجازة أمومة', 90, 1, 1),
            ('إجازة حج', 15, 1, 1)
        ]

        cursor.executemany("""
            INSERT INTO leave_type (name, max_days, requires_approval, is_active)
            VALUES (?, ?, ?, ?)
        """, leave_types)

        # إنشاء جدول طلبات الإجازة
        cursor.execute("""
            CREATE TABLE leave_request (
                id INTEGER NOT NULL PRIMARY KEY,
                employee_id INTEGER NOT NULL,
                leave_type_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                total_days INTEGER NOT NULL,
                reason TEXT,
                status VARCHAR(50) DEFAULT 'PENDING',
                approved_by INTEGER,
                approved_at DATETIME,
                rejection_reason TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(employee_id) REFERENCES employee(id),
                FOREIGN KEY(leave_type_id) REFERENCES leave_type(id),
                FOREIGN KEY(approved_by) REFERENCES user(id)
            )
        """)

        # إنشاء جدول سجلات التدقيق
        cursor.execute("""
            CREATE TABLE audit_log (
                id INTEGER NOT NULL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                action VARCHAR(50) NOT NULL,
                entity VARCHAR(50) NOT NULL,
                entity_id INTEGER,
                details TEXT,
                ip_address VARCHAR(50),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(user_id) REFERENCES user(id)
            )
        """)

        # حفظ التغييرات
        conn.commit()
        conn.close()

        print("تم إنشاء قاعدة البيانات الجديدة بنجاح!")
        print("بيانات المدير الافتراضي:")
        print("  اسم المستخدم: admin")
        print("  كلمة المرور: admin123")
        print("  البريد الإلكتروني: <EMAIL>")

        return True

    except Exception as e:
        print(f"حدث خطأ أثناء إعادة تعيين قاعدة البيانات: {e}")
        return False
