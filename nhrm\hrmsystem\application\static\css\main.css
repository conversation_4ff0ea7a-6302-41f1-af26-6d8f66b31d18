/* Main CSS file for HRM System */

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* General styles */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fc;
}

/* Card styles */
.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.75rem 1.25rem;
}

/* Button styles */
.btn {
    border-radius: 0.35rem;
}

/* Table styles */
.table th {
    font-weight: 600;
}

/* Form styles */
.form-control, .form-select {
    border-radius: 0.35rem;
}

/* Custom colors */
.bg-gradient-primary {
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    background-size: cover;
}

/* Sidebar styles */
.sidebar {
    min-height: 100vh;
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    background-size: cover;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background-color: white;
    }
    
    .container, .container-fluid {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
}

/* Employee card styles */
.employee-card {
    transition: transform 0.3s;
}

.employee-card:hover {
    transform: translateY(-5px);
}

/* Status badge colors */
.status-active {
    background-color: #1cc88a;
}

.status-absent {
    background-color: #e74a3b;
}

.status-assigned {
    background-color: #4e73df;
}

.status-on-leave {
    background-color: #f6c23e;
}

.status-suspended {
    background-color: #858796;
}

.status-scattered {
    background-color: #36b9cc;
}

.status-medical {
    background-color: #5a5c69;
}

/* QR code styles */
.qr-code-container {
    text-align: center;
    margin-bottom: 1rem;
}

.qr-code-container img {
    max-width: 200px;
}

/* Employee profile styles */
.employee-profile-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Print template styles */
.employee-header {
    border-bottom: 2px solid #4e73df;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
}

.employee-info {
    margin-bottom: 1.5rem;
}

.employee-info h4 {
    color: #4e73df;
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.employee-info-item {
    margin-bottom: 0.5rem;
}

.employee-info-label {
    font-weight: 600;
}

/* Calendar styles */
.fc-event {
    cursor: pointer;
}

/* Dashboard card styles */
.card-counter {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin: 5px;
    padding: 20px 10px;
    background-color: #fff;
    height: 100px;
    border-radius: 5px;
    transition: .3s linear all;
}

.card-counter:hover {
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.3);
    transition: .3s linear all;
}

.card-counter i {
    font-size: 5em;
    opacity: 0.2;
}

.card-counter .count-numbers {
    position: absolute;
    right: 35px;
    top: 20px;
    font-size: 32px;
    display: block;
}

.card-counter .count-name {
    position: absolute;
    right: 35px;
    top: 65px;
    font-style: italic;
    text-transform: capitalize;
    opacity: 0.5;
    display: block;
    font-size: 18px;
}
