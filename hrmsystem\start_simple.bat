@echo off
chcp 65001 >nul
title نظام إدارة الموارد البشرية

echo ================================================================
echo                نظام إدارة الموارد البشرية
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7 أو أحدث
    pause
    exit /b 1
)

echo ✓ تم العثور على Python

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "application\static\uploads" mkdir application\static\uploads
if not exist "application\static\uploads\employees" mkdir application\static\uploads\employees

echo ✓ تم إنشاء المجلدات المطلوبة

echo.
echo بدء تشغيل النظام...
echo.

REM بدء التطبيق
python run_simple.py

REM في حالة الخروج غير المتوقع
echo.
echo ================================================================
echo تم إيقاف النظام
echo ================================================================
echo.

if errorlevel 1 (
    echo حدث خطأ أثناء تشغيل النظام
    echo.
)

echo اضغط أي مفتاح للخروج...
pause >nul
