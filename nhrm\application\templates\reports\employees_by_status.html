{% extends 'layouts/base.html' %}

{% block title %}الموظفين حسب الحالة{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
    }

    .status-badge {
        display: inline-block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الموظفين حسب الحالة</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>توزيع الموظفين حسب الحالة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">إحصائيات الحالات</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>الحالة</th>
                                                    <th>عدد الموظفين</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for status, count in status_counts %}
                                                <tr>
                                                    <td>
                                                        {% if status == 'مستمر' %}
                                                            <span class="status-badge" style="background-color: #28a745;"></span>
                                                        {% elif status == 'غياب وهروب' %}
                                                            <span class="status-badge" style="background-color: #dc3545;"></span>
                                                        {% elif status == 'مكلف' %}
                                                            <span class="status-badge" style="background-color: #007bff;"></span>
                                                        {% elif status == 'اجازة' %}
                                                            <span class="status-badge" style="background-color: #17a2b8;"></span>
                                                        {% elif status == 'ايقاف عن العمل' %}
                                                            <span class="status-badge" style="background-color: #ffc107;"></span>
                                                        {% elif status == 'متفرق' %}
                                                            <span class="status-badge" style="background-color: #6c757d;"></span>
                                                        {% elif status == 'عيادة طبية' %}
                                                            <span class="status-badge" style="background-color: #20c997;"></span>
                                                        {% else %}
                                                            <span class="status-badge" style="background-color: #6c757d;"></span>
                                                        {% endif %}
                                                        {{ status }}
                                                    </td>
                                                    <td>{{ count }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('statusChart').getContext('2d');

        const labels = {{ labels|safe }};
        const data = {{ values|safe }};
        const colors = {{ colors|safe }};

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace(')', ', 0.8)')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    title: {
                        display: true,
                        text: 'توزيع الموظفين حسب الحالة',
                        font: {
                            size: 16
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
