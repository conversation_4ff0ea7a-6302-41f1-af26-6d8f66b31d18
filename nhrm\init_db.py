from application import create_app, db
from application.models import User, Department, Employee, LeaveType, LeaveRequest
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta, timezone
import random

app = create_app()

def init_db():
    with app.app_context():
        # Create tables
        db.create_all()

        # Force reinitialization
        print("Initializing database with sample data...")

        # Clear existing data
        db.session.query(LeaveRequest).delete()
        db.session.query(Employee).delete()
        db.session.query(LeaveType).delete()
        db.session.query(Department).delete()
        db.session.query(User).delete()
        db.session.commit()

        # Create admin user
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),
            role="admin",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(admin_user)

        # Create regular user
        regular_user = User(
            username="user",
            email="<EMAIL>",
            password=generate_password_hash("user123"),
            role="employee",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(regular_user)

        # Create departments (units)
        departments = [
            Department(name="القيادة العامة", description="وحدة القيادة العامة للقوات المسلحة"),
            Department(name="الكتيبة الأولى", description="وحدة الكتيبة الأولى"),
            Department(name="الكتيبة الثانية", description="وحدة الكتيبة الثانية"),
            Department(name="الكتيبة الثالثة", description="وحدة الكتيبة الثالثة"),
            Department(name="الإدارة العامة", description="وحدة الإدارة العامة")
        ]

        for dept in departments:
            db.session.add(dept)

        db.session.commit()

        # Create leave types
        leave_types = [
            LeaveType(name="إجازة سنوية", description="الإجازة السنوية المستحقة", max_days_per_year=30),
            LeaveType(name="إجازة مرضية", description="إجازة للظروف الصحية", max_days_per_year=15),
            LeaveType(name="إجازة طارئة", description="إجازة للظروف الطارئة", max_days_per_year=7),
            LeaveType(name="إجازة دراسية", description="إجازة للدراسة والتطوير المهني", max_days_per_year=30)
        ]

        for lt in leave_types:
            db.session.add(lt)

        db.session.commit()

        # Create employees
        military_ranks = ["عقيد", "مقدم", "رائد", "نقيب", "ملازم أول", "ملازم"]

        for i in range(1, 21):
            rank = random.choice(military_ranks)

            hire_date = datetime.now(timezone.utc) - timedelta(days=random.randint(30, 3650))

            # فئة الموظف (ضباط، ضباط صف، موظف)
            category_choices = ["ضباط", "ضباط صف", "موظف"]
            category = random.choice(category_choices)

            # فصيلة الدم
            blood_types = ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"]
            blood_type = random.choice(blood_types)

            # مكان الميلاد
            birth_places = ["طرابلس", "بنغازي", "مصراتة", "سبها", "الزاوية", "زليتن"]
            birth_place = random.choice(birth_places)

            # المؤهل العلمي
            education_choices = ["بكالوريوس هندسة", "ليسانس حقوق", "دبلوم عالي", "ماجستير", "دكتوراه", "ثانوية عامة"]
            education = random.choice(education_choices)

            # تاريخ الحصول على المؤهل
            education_date = (hire_date - timedelta(days=random.randint(365, 3650))).date()

            # تاريخ آخر ترقية
            last_promotion_date = None
            if random.random() > 0.3:  # 70% من الموظفين لديهم ترقية
                last_promotion_date = (hire_date + timedelta(days=random.randint(180, 1825))).date()  # بين 6 أشهر و5 سنوات من تاريخ التعيين

            # حالة الموظف
            status_choices = [
                ('مستمر', 0.7),  # 70% مستمر
                ('غياب وهروب', 0.05),  # 5% غياب وهروب
                ('مكلف', 0.05),  # 5% مكلف
                ('اجازة', 0.1),  # 10% اجازة
                ('ايقاف عن العمل', 0.03),  # 3% ايقاف عن العمل
                ('متفرق', 0.04),  # 4% متفرق
                ('عيادة طبية', 0.03)  # 3% عيادة طبية
            ]

            # اختيار الحالة بناءً على الاحتمالات
            r = random.random()
            cumulative = 0
            employee_status = 'مستمر'  # القيمة الافتراضية
            status_notes = None

            for status, prob in status_choices:
                cumulative += prob
                if r <= cumulative:
                    employee_status = status
                    if status != 'مستمر':
                        status_notes = f"ملاحظات حول حالة {status} للموظف {i}"
                    break

            # المصرف ورقم الحساب
            bank_choices = ["مصرف الجمهورية", "مصرف الوحدة", "مصرف التجاري الوطني", "مصرف شمال أفريقيا"]
            bank_name = random.choice(bank_choices)
            bank_account = f"{random.randint(1000000, 9999999)}"

            # الرقم الوطني
            national_id = f"{random.randint(**********, ********99)}"

            # الوحدة - إدخال حر حسب المستخدم
            department_names = ["القيادة العامة", "الكتيبة الأولى", "الكتيبة الثانية", "الكتيبة الثالثة", "الإدارة العامة"]
            department_name = random.choice(department_names)

            employee = Employee(
                name=f"موظف {i}",
                military_id=f"MIL{100000 + i}",
                military_rank=rank,
                position=f"العمل المكلف به {i}",
                department_name=department_name,
                national_id=national_id,
                blood_type=blood_type,
                employee_category=category,
                birth_place=birth_place,
                education=education,
                education_date=education_date,
                bank_name=bank_name,
                bank_account=bank_account,
                phone=f"05{random.randint(********, ********)}",
                address=f"عنوان الموظف {i}",
                hire_date=hire_date.date(),
                last_promotion_date=last_promotion_date,
                employee_status=employee_status,
                status_notes=status_notes,
                date_of_birth=(hire_date - timedelta(days=random.randint(8000, 15000))).date()
            )

            db.session.add(employee)

        db.session.commit()

        # Create leave requests
        employees = Employee.query.all()

        for i in range(1, 31):
            employee = random.choice(employees)
            leave_type = random.choice(leave_types)

            start_date = datetime.now(timezone.utc) + timedelta(days=random.randint(-30, 30))
            end_date = start_date + timedelta(days=random.randint(1, 10))

            status_choices = ["pending", "approved", "rejected"]
            status = random.choice(status_choices)

            leave_request = LeaveRequest(
                employee_id=employee.id,
                leave_type_id=leave_type.id,
                start_date=start_date.date(),
                end_date=end_date.date(),
                total_days=(end_date - start_date).days + 1,
                reason=f"سبب الإجازة {i}",
                status=status,
                created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 10))
            )

            if status != "pending":
                leave_request.approved_by = admin_user.id
                leave_request.approved_at = datetime.now(timezone.utc) - timedelta(days=random.randint(0, 5))
                leave_request.comments = "تمت المراجعة والموافقة" if status == "approved" else "تم الرفض لأسباب تنظيمية"

            db.session.add(leave_request)

        db.session.commit()

        print("Database initialized successfully!")

if __name__ == "__main__":
    init_db()
