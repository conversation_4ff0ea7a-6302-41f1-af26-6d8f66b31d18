{% extends 'layouts/base.html' %}

{% block title %}تفاصيل الإجازة - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar-day me-2"></i>تفاصيل الإجازة #{{ leave_request.id }}
        </h5>
        <div>
            {% if current_user.role == 'admin' and leave_request.status == 'pending' %}
            <a href="{{ url_for('leaves.approve', id=leave_request.id) }}" class="btn btn-success btn-sm">
                <i class="fas fa-check-circle me-1"></i>الموافقة/الرفض
            </a>
            {% endif %}
            <a href="{{ url_for('leaves.index') }}" class="btn btn-light btn-sm ms-2">
                <i class="fas fa-list me-1"></i>قائمة الإجازات
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>بيانات الموظف
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">الاسم</th>
                                    <td>
                                        <a href="{{ url_for('employees.view', id=employee.id) }}">
                                            {{ employee.name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>الرقم العسكري</th>
                                    <td>{{ employee.military_id }}</td>
                                </tr>
                                <tr>
                                    <th>الرتبة</th>
                                    <td>{{ employee.military_rank }}</td>
                                </tr>
                                <tr>
                                    <th>الوحدة</th>
                                    <td>{{ employee.department_name }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>بيانات الإجازة
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">نوع الإجازة</th>
                                    <td>{{ leave_type.name }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ البداية</th>
                                    <td>{{ leave_request.start_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ النهاية</th>
                                    <td>{{ leave_request.end_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>عدد الأيام</th>
                                    <td>{{ leave_request.total_days }} يوم</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">تاريخ الطلب</th>
                                    <td>{{ leave_request.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        {% if leave_request.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                        {% elif leave_request.status == 'approved' %}
                                        <span class="badge bg-success">تمت الموافقة</span>
                                        {% elif leave_request.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if leave_request.status != 'pending' %}
                                <tr>
                                    <th>تمت المراجعة بواسطة</th>
                                    <td>{{ approver_name }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ المراجعة</th>
                                    <td>{{ leave_request.approved_at.strftime('%Y-%m-%d %H:%M') if leave_request.approved_at else 'غير متوفر' }}</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-comment-alt me-2"></i>سبب الإجازة والملاحظات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>سبب الإجازة:</h6>
                            <p class="border rounded p-3 bg-light">{{ leave_request.reason or 'لم يتم تحديد سبب' }}</p>
                        </div>

                        {% if leave_request.comments %}
                        <div>
                            <h6>ملاحظات المراجعة:</h6>
                            <p class="border rounded p-3 bg-light">{{ leave_request.comments }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
