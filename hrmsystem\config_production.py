import os
from datetime import timedelta

class ProductionConfig:
    """إعدادات الإنتاج المحسنة للأداء والأمان"""
    
    # إعدادات الأمان
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-super-secret-production-key-change-this'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///hrm_production.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0,
        'echo': False
    }
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # 8 ساعات للإنتاج
    SESSION_COOKIE_SECURE = True  # HTTPS فقط
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات الرفع
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'application/static/uploads')
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024  # 5MB
    
    # إعدادات المدير الافتراضي
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = 'admin123'
    ADMIN_EMAIL = '<EMAIL>'
    
    # إعدادات الصفحات
    EMPLOYEES_PER_PAGE = 20  # زيادة عدد العناصر في الصفحة للإنتاج
    LEAVES_PER_PAGE = 20
    AUDIT_LOGS_PER_PAGE = 50
    
    # أيام نهاية الأسبوع
    WEEKEND_DAYS = [4, 5]  # الجمعة والسبت
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300  # 5 دقائق
    
    # إعدادات الأداء
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=12)
    
    # إعدادات التسجيل
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/hrm_production.log'
    
    # إعدادات قاعدة البيانات المحسنة
    DATABASE_RESET_ENABLED = True  # يمكن تعطيلها في الإنتاج
    DATABASE_BACKUP_ENABLED = True
    
    # إعدادات الأمان الإضافية
    SECURITY_PASSWORD_SALT = os.environ.get('SECURITY_PASSWORD_SALT') or 'your-password-salt'
    
    # إعدادات البريد الإلكتروني (للإشعارات)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات التطبيق
    PREFERRED_URL_SCHEME = 'https'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق للإنتاج"""
        import logging
        from logging.handlers import RotatingFileHandler
        import os
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # إعداد تسجيل السجلات
        file_handler = RotatingFileHandler(
            ProductionConfig.LOG_FILE, 
            maxBytes=10240000, 
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('HRM System startup - Production Mode')

class DevelopmentConfig:
    """إعدادات التطوير"""
    
    # إعدادات الأمان
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///hrm_dev.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'echo': True  # إظهار استعلامات SQL في التطوير
    }
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = False  # HTTP مسموح في التطوير
    SESSION_COOKIE_HTTPONLY = True
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # إعدادات الرفع
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'application/static/uploads')
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024
    
    # إعدادات المدير الافتراضي
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = 'admin123'
    ADMIN_EMAIL = '<EMAIL>'
    
    # إعدادات الصفحات
    EMPLOYEES_PER_PAGE = 10
    LEAVES_PER_PAGE = 10
    AUDIT_LOGS_PER_PAGE = 20
    
    # أيام نهاية الأسبوع
    WEEKEND_DAYS = [4, 5]
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE = 'null'  # بدون تخزين مؤقت في التطوير
    
    # إعدادات قاعدة البيانات
    DATABASE_RESET_ENABLED = True
    DATABASE_BACKUP_ENABLED = False  # بدون نسخ احتياطية في التطوير
    
    # إعدادات التسجيل
    LOG_LEVEL = 'DEBUG'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق للتطوير"""
        app.logger.setLevel(logging.DEBUG)
        app.logger.info('HRM System startup - Development Mode')

# تحديد الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
