from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON><PERSON>ield, SelectField, DateField, TextAreaField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Email, Optional, Length

class EmployeeForm(FlaskForm):
    name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=3, max=150)])
    # تعديل المنصب إلى العمل المكلف به
    position = StringField('العمل المكلف به', validators=[DataRequired(), Length(max=100)])
    # تعديل القسم إلى الوحدة - إدخال حر حسب المستخدم
    department_name = StringField('الوحدة', validators=[DataRequired(), Length(max=100)])
    military_rank = StringField('الرتبة العسكرية', validators=[Length(max=50)])
    military_id = StringField('الرقم العسكري', validators=[DataRequired(), Length(max=50)])
    # الرقم الوطني
    national_id = StringField('الرقم الوطني', validators=[DataRequired(), Length(max=50)])
    # فصيلة الدم
    blood_type = SelectField('فصيلة الدم', choices=[
        ('', 'اختر فصيلة الدم'),
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-')
    ], validators=[Optional()])
    # الفئة (ضباط، ضباط صف، موظف)
    employee_category = SelectField('الفئة', choices=[
        ('', 'اختر الفئة'),
        ('ضباط', 'ضباط'),
        ('ضباط صف', 'ضباط صف'),
        ('موظف', 'موظف')
    ], validators=[Optional()])
    # حالة الموظف
    employee_status = SelectField('حالة الموظف', choices=[
        ('', 'اختر حالة الموظف'),
        ('مستمر', 'مستمر'),
        ('غياب وهروب', 'غياب وهروب'),
        ('مكلف', 'مكلف'),
        ('اجازة', 'اجازة'),
        ('ايقاف عن العمل', 'ايقاف عن العمل'),
        ('متفرق', 'متفرق'),
        ('عيادة طبية', 'عيادة طبية')
    ], validators=[Optional()])
    # ملاحظات حول حالة الموظف
    status_notes = TextAreaField('ملاحظات حول الحالة', validators=[Optional(), Length(max=500)])
    # مكان الميلاد
    birth_place = StringField('مكان الميلاد', validators=[Optional(), Length(max=100)])
    # المؤهل العلمي
    education = StringField('المؤهل العلمي', validators=[Optional(), Length(max=100)])
    # تاريخ الحصول على المؤهل
    education_date = DateField('تاريخ الحصول على المؤهل', format='%Y-%m-%d', validators=[Optional()])
    # المصرف
    bank_name = StringField('المصرف', validators=[Optional(), Length(max=100)])
    # رقم حساب المصرف
    bank_account = StringField('رقم حساب المصرف', validators=[Optional(), Length(max=50)])
    phone = StringField('رقم الهاتف', validators=[Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Email(), Length(max=150)])
    # تعديل العنوان إلى السكن الحالي
    address = TextAreaField('السكن الحالي', validators=[Optional(), Length(max=500)])
    date_of_birth = DateField('تاريخ الميلاد', format='%Y-%m-%d', validators=[Optional()])
    hire_date = DateField('تاريخ التعيين', format='%Y-%m-%d', validators=[DataRequired()])
    # تاريخ آخر ترقية
    last_promotion_date = DateField('تاريخ آخر ترقية', format='%Y-%m-%d', validators=[Optional()])
    profile_image = FileField('الصورة الشخصية', validators=[
        FileAllowed(['jpg', 'png', 'jpeg'], 'يسمح فقط بملفات الصور (jpg, png, jpeg)')
    ])
    submit = SubmitField('حفظ')

class EmployeeSearchForm(FlaskForm):
    search = StringField('بحث', validators=[Optional()])
    department = StringField('الوحدة', validators=[Optional()])
    rank = SelectField('الرتبة', validators=[Optional()])
    submit = SubmitField('بحث')

class ImportForm(FlaskForm):
    file = FileField('ملف Excel', validators=[DataRequired(), FileAllowed(['xlsx', 'xls'], 'يجب أن يكون الملف بتنسيق Excel')])
    update_existing = BooleanField('تحديث البيانات الموجودة')
    submit = SubmitField('استيراد')
