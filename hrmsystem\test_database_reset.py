#!/usr/bin/env python3
"""
اختبار ميزة إعادة تعيين قاعدة البيانات
Test Database Reset Functionality

هذا النص يختبر:
- وظيفة إعادة تعيين قاعدة البيانات
- إنشاء المستخدم الافتراضي
- سلامة البيانات بعد إعادة التعيين
"""

import os
import sys
import sqlite3
import tempfile
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_reset_function():
    """اختبار دالة إعادة تعيين قاعدة البيانات"""
    print("=" * 60)
    print("اختبار دالة إعادة تعيين قاعدة البيانات")
    print("=" * 60)
    
    try:
        # إنشاء قاعدة بيانات مؤقتة للاختبار
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
            temp_db_path = temp_db.name
        
        print(f"إنشاء قاعدة بيانات مؤقتة: {temp_db_path}")
        
        # إنشاء التطبيق للاختبار
        from application import create_app
        app = create_app('development')
        
        # تحديث مسار قاعدة البيانات للاختبار
        app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{temp_db_path}'
        
        with app.app_context():
            # اختبار دالة إعادة التعيين
            from application.utilities import reset_database_secure
            
            print("تشغيل دالة إعادة التعيين...")
            result = reset_database_secure()
            
            if result:
                print("✓ تم تشغيل دالة إعادة التعيين بنجاح")
                
                # التحقق من وجود قاعدة البيانات
                if os.path.exists(temp_db_path):
                    print("✓ تم إنشاء قاعدة البيانات")
                    
                    # التحقق من الجداول والبيانات
                    conn = sqlite3.connect(temp_db_path)
                    cursor = conn.cursor()
                    
                    # التحقق من جدول المستخدمين
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
                    if cursor.fetchone():
                        print("✓ تم إنشاء جدول المستخدمين")
                        
                        # التحقق من المستخدم الافتراضي
                        cursor.execute("SELECT username, email, is_admin FROM user WHERE username='admin'")
                        admin_user = cursor.fetchone()
                        
                        if admin_user:
                            print(f"✓ تم إنشاء المستخدم الافتراضي: {admin_user[0]} ({admin_user[1]})")
                            if admin_user[2]:
                                print("✓ المستخدم الافتراضي لديه صلاحيات المدير")
                            else:
                                print("✗ المستخدم الافتراضي ليس لديه صلاحيات المدير")
                        else:
                            print("✗ لم يتم العثور على المستخدم الافتراضي")
                    else:
                        print("✗ لم يتم إنشاء جدول المستخدمين")
                    
                    # التحقق من الجداول الأخرى
                    tables_to_check = ['employee', 'leave_type', 'leave_request', 'audit_log']
                    for table in tables_to_check:
                        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                        if cursor.fetchone():
                            print(f"✓ تم إنشاء جدول {table}")
                        else:
                            print(f"✗ لم يتم إنشاء جدول {table}")
                    
                    conn.close()
                else:
                    print("✗ لم يتم إنشاء قاعدة البيانات")
            else:
                print("✗ فشل في تشغيل دالة إعادة التعيين")
        
        # تنظيف الملفات المؤقتة
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)
            print("تم حذف قاعدة البيانات المؤقتة")
        
        print("\n" + "=" * 60)
        print("انتهى اختبار دالة إعادة تعيين قاعدة البيانات")
        print("=" * 60)
        
        return result
        
    except Exception as e:
        print(f"✗ خطأ في الاختبار: {e}")
        return False

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("\n" + "=" * 60)
    print("اختبار بدء تشغيل التطبيق")
    print("=" * 60)
    
    try:
        # اختبار إنشاء التطبيق في وضع التطوير
        from application import create_app
        
        print("اختبار وضع التطوير...")
        app_dev = create_app('development')
        if app_dev:
            print("✓ تم إنشاء التطبيق في وضع التطوير")
        else:
            print("✗ فشل في إنشاء التطبيق في وضع التطوير")
        
        # اختبار إنشاء التطبيق في وضع الإنتاج
        print("اختبار وضع الإنتاج...")
        try:
            app_prod = create_app('production')
            if app_prod:
                print("✓ تم إنشاء التطبيق في وضع الإنتاج")
            else:
                print("✗ فشل في إنشاء التطبيق في وضع الإنتاج")
        except Exception as e:
            print(f"تحذير: مشكلة في وضع الإنتاج - {e}")
        
        # اختبار الوضع الافتراضي
        print("اختبار الوضع الافتراضي...")
        app_default = create_app()
        if app_default:
            print("✓ تم إنشاء التطبيق في الوضع الافتراضي")
        else:
            print("✗ فشل في إنشاء التطبيق في الوضع الافتراضي")
        
        print("\n" + "=" * 60)
        print("انتهى اختبار بدء تشغيل التطبيق")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار بدء التشغيل: {e}")
        return False

def test_routes_availability():
    """اختبار توفر المسارات"""
    print("\n" + "=" * 60)
    print("اختبار توفر المسارات")
    print("=" * 60)
    
    try:
        from application import create_app
        app = create_app('development')
        
        with app.app_context():
            # قائمة المسارات المطلوب اختبارها
            routes_to_test = [
                'dashboard.index',
                'dashboard.reset_database',
                'auth.login',
                'employees.index',
                'leaves.index'
            ]
            
            from flask import url_for
            
            for route in routes_to_test:
                try:
                    url = url_for(route)
                    print(f"✓ المسار {route}: {url}")
                except Exception as e:
                    print(f"✗ المسار {route}: خطأ - {e}")
        
        print("\n" + "=" * 60)
        print("انتهى اختبار توفر المسارات")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار المسارات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("بدء اختبارات نظام إدارة الموارد البشرية")
    print(f"التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار دالة إعادة تعيين قاعدة البيانات
    if test_database_reset_function():
        tests_passed += 1
    
    # اختبار بدء تشغيل التطبيق
    if test_application_startup():
        tests_passed += 1
    
    # اختبار توفر المسارات
    if test_routes_availability():
        tests_passed += 1
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("نتائج الاختبارات النهائية")
    print("=" * 60)
    print(f"الاختبارات المنجحة: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ جميع الاختبارات نجحت!")
        print("النظام جاهز للاستخدام")
    else:
        print("✗ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
