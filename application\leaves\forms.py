from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SelectField, DateField, TextAreaField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Optional, Length

class LeaveRequestForm(FlaskForm):
    employee_id = SelectField('الموظف', coerce=int, validators=[DataRequired()])
    leave_type = StringField('نوع الإجازة', validators=[DataRequired(), Length(max=100)])
    initial_balance = StringField('رصيد الإجازة', validators=[DataRequired()])
    leave_balance = HiddenField('رصيد الإجازة المتبقي')
    start_date = DateField('تاريخ البداية', format='%Y-%m-%d', validators=[DataRequired()])
    end_date = DateField('تاريخ النهاية', format='%Y-%m-%d', validators=[DataRequired()])
    total_days = HiddenField('عدد الأيام')
    reason = TextAreaField('سبب الإجازة', validators=[Optional(), Length(max=500)])
    submit = SubmitField('تقديم الطلب')

class LeaveApprovalForm(FlaskForm):
    status = SelectField('الحالة', choices=[
        ('approved', 'موافقة'),
        ('rejected', 'رفض'),
        ('pending', 'قيد الانتظار')
    ], validators=[DataRequired()])
    comments = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    submit = SubmitField('تحديث الحالة')

class LeaveSearchForm(FlaskForm):
    employee = SelectField('الموظف', coerce=int, validators=[Optional()])
    leave_type = SelectField('نوع الإجازة', coerce=int, validators=[Optional()])
    status = SelectField('الحالة', choices=[
        (0, 'جميع الحالات'),
        (1, 'قيد الانتظار'),
        (2, 'موافقة'),
        (3, 'رفض')
    ], validators=[Optional()])
    date_from = DateField('من تاريخ', format='%Y-%m-%d', validators=[Optional()])
    date_to = DateField('إلى تاريخ', format='%Y-%m-%d', validators=[Optional()])
    submit = SubmitField('بحث')
