#!/usr/bin/env python3
"""
إصلاح مخطط قاعدة البيانات - نظام إدارة الموارد البشرية
Fix Database Schema - HR Management System

هذا النص يقوم بإصلاح مخطط قاعدة البيانات لإضافة الأعمدة المفقودة
"""

import os
import sys
import sqlite3
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_user_table():
    """إصلاح جدول المستخدمين لإضافة الأعمدة المفقودة"""
    
    # البحث عن قاعدة البيانات
    db_paths = [
        'instance/hrm.db',
        'app.db',
        'hrm.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("لم يتم العثور على قاعدة البيانات")
        return False
    
    print(f"العمل على قاعدة البيانات: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من هيكل جدول المستخدمين الحالي
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("الأعمدة الموجودة حالياً:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # قائمة الأعمدة المطلوبة
        required_columns = {
            'role': 'VARCHAR(50) DEFAULT "employee"',
            'created_at': 'DATETIME',
            'updated_at': 'DATETIME',
            'last_login': 'DATETIME',
            'full_name': 'TEXT',
            'status': 'VARCHAR(50) DEFAULT "active"',
            'phone': 'TEXT',
            'profile_image': 'TEXT',
            'is_admin': 'BOOLEAN DEFAULT 0'
        }
        
        # إضافة الأعمدة المفقودة
        for col_name, col_type in required_columns.items():
            if col_name not in column_names:
                try:
                    cursor.execute(f"ALTER TABLE user ADD COLUMN {col_name} {col_type}")
                    print(f"✓ تم إضافة العمود: {col_name}")
                except sqlite3.OperationalError as e:
                    print(f"✗ خطأ في إضافة العمود {col_name}: {e}")
        
        # التحقق من وجود مستخدم admin وتحديث صلاحياته
        cursor.execute("SELECT id, username, is_admin FROM user WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            user_id, username, is_admin = admin_user
            if not is_admin:
                cursor.execute("UPDATE user SET is_admin = 1, role = 'admin' WHERE username = 'admin'")
                print("✓ تم تحديث صلاحيات المستخدم admin")
        else:
            # إنشاء مستخدم admin إذا لم يكن موجوداً
            from werkzeug.security import generate_password_hash
            admin_password = generate_password_hash('admin123')
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO user (username, email, password, role, is_admin, created_at, full_name, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'admin',
                '<EMAIL>',
                admin_password,
                'admin',
                1,
                now,
                'مدير النظام',
                'active'
            ))
            print("✓ تم إنشاء مستخدم admin جديد")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة النهائية
        cursor.execute("PRAGMA table_info(user)")
        final_columns = cursor.fetchall()
        
        print("\nهيكل الجدول بعد الإصلاح:")
        for col in final_columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        print("\n✓ تم إصلاح قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from application import create_app
        app = create_app('development')
        
        with app.app_context():
            from application.models import User
            
            # اختبار الاستعلام
            users = User.query.all()
            print(f"✓ تم العثور على {len(users)} مستخدم في قاعدة البيانات")
            
            # التحقق من المستخدم admin
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print(f"✓ المستخدم admin موجود - is_admin: {admin.is_admin}")
            else:
                print("✗ المستخدم admin غير موجود")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إصلاح مخطط قاعدة البيانات - نظام إدارة الموارد البشرية")
    print("=" * 60)
    print(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # إصلاح جدول المستخدمين
    if fix_user_table():
        print("\n" + "=" * 60)
        print("اختبار الاتصال بقاعدة البيانات")
        print("=" * 60)
        
        # اختبار الاتصال
        if test_database_connection():
            print("\n✓ تم إصلاح قاعدة البيانات بنجاح!")
            print("يمكنك الآن تشغيل التطبيق باستخدام:")
            print("  python run_simple.py")
        else:
            print("\n✗ فشل في اختبار قاعدة البيانات")
    else:
        print("\n✗ فشل في إصلاح قاعدة البيانات")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
