{% extends 'layouts/base.html' %}

{% block title %}الموظفين حسب الرتبة{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الموظفين حسب الرتبة</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>توزيع الموظفين حسب الرتبة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <canvas id="rankChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">إحصائيات الرتب</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>الرتبة</th>
                                                    <th>عدد الموظفين</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for rank, count in rank_counts %}
                                                <tr>
                                                    <td>{{ rank }}</td>
                                                    <td>{{ count }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('rankChart').getContext('2d');

        const labels = {{ labels|safe }};
        const data = {{ values|safe }};

        // Generate colors
        const colors = [];
        for (let i = 0; i < labels.length; i++) {
            const hue = (i * 137.5) % 360;
            colors.push(`hsl(${hue}, 70%, 60%)`);
        }

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: data,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('60%', '50%')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'توزيع الموظفين حسب الرتبة',
                        font: {
                            size: 16
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد الموظفين'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الرتبة'
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
