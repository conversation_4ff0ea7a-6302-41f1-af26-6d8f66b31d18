<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        {% if report_type == 'type' %}
        تقرير الإجازات حسب النوع
        {% endif %}
    </title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.rtl.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12pt;
            line-height: 1.5;
            background-color: white;
            color: black;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
        .no-print {
            display: none !important;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dc3545;
            background: linear-gradient(to bottom, #f8f9fa, #ffffff);
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header h2 {
            font-size: 22pt;
            font-weight: bold;
            margin: 5px 0;
            color: #dc3545;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        }
        .header h3 {
            font-size: 18pt;
            font-weight: bold;
            margin: 5px 0;
            color: #c82333;
        }
        .header h4 {
            font-size: 16pt;
            font-weight: bold;
            margin: 5px 0;
            color: #bd2130;
        }
        .report-title {
            text-align: center;
            margin: 20px 0;
            font-size: 18pt;
            font-weight: bold;
            color: #dc3545;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
            padding: 10px 0;
            border-bottom: 2px solid #dc3545;
            border-top: 2px solid #dc3545;
            background: linear-gradient(to right, #ffffff, #f8f9fa, #ffffff);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-radius: 5px;
            overflow: hidden;
        }
        .table th, .table td {
            border: 1px solid #e9ecef;
            padding: 10px;
            text-align: right;
        }
        .table th {
            background-color: #dc3545;
            color: white;
            font-weight: bold;
            border-color: #c82333;
        }
        .table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .table tr:hover {
            background-color: #e9ecef;
        }
        .table-group-header {
            background-color: #c82333;
            color: white;
            font-weight: bold;
            font-size: 14pt;
        }
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 80%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 50rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .bg-success {
            background-color: #28a745;
            color: white;
        }
        .bg-danger {
            background-color: #dc3545;
            color: white;
        }
        .bg-info {
            background-color: #17a2b8;
            color: white;
        }
        .bg-warning {
            background-color: #ffc107;
            color: black;
        }
        .bg-secondary {
            background-color: #6c757d;
            color: white;
        }
        .bg-primary {
            background-color: #007bff;
            color: white;
        }
        .bg-dark {
            background-color: #343a40;
            color: white;
        }
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            padding-top: 10px;
            border-top: 2px solid #dc3545;
            font-size: 10pt;
            background: linear-gradient(to top, #f8f9fa, #ffffff);
            padding: 10px 0;
        }
        .print-container {
            max-width: 21cm;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 1cm;
            min-height: 29.7cm;
            position: relative;
        }
        tfoot th {
            background-color: #e9ecef;
            color: #212529;
            font-weight: bold;
        }
        @media print {
            body {
                background-color: white;
            }
            .print-container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            .badge {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
            .bg-success {
                background-color: white !important;
                color: black !important;
                border: 1px solid #28a745 !important;
            }
            .bg-danger {
                background-color: white !important;
                color: black !important;
                border: 1px solid #dc3545 !important;
            }
            .bg-info {
                background-color: white !important;
                color: black !important;
                border: 1px solid #17a2b8 !important;
            }
            .bg-warning {
                background-color: white !important;
                color: black !important;
                border: 1px solid #ffc107 !important;
            }
            .bg-secondary {
                background-color: white !important;
                color: black !important;
                border: 1px solid #6c757d !important;
            }
            .bg-primary {
                background-color: white !important;
                color: black !important;
                border: 1px solid #007bff !important;
            }
            .bg-dark {
                background-color: white !important;
                color: black !important;
                border: 1px solid #343a40 !important;
            }
            .no-print {
                display: none !important;
            }
            .table th {
                background-color: #e9ecef !important;
                color: black !important;
                border: 1px solid #ddd !important;
            }
            .table-group-header {
                background-color: #e9ecef !important;
                color: black !important;
                border: 1px solid #ddd !important;
            }
            .table {
                box-shadow: none !important;
            }
            .header, .report-title {
                background: none !important;
                box-shadow: none !important;
                text-shadow: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="print-container">
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h2>
                {% if report_type == 'type' %}
                <i class="fas fa-calendar-alt"></i> تقرير الإجازات حسب النوع
                {% endif %}
            </h2>
            <div>
                <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة إلى لوحة التحكم
                </a>
                <button class="btn btn-success" onclick="exportTableToExcel('reportTable', 'تقرير_الإجازات')">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </button>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>

        <div class="header">
            <h2>وزارة الداخلية</h2>
            <h3>مديرية أمن الخمس</h3>
            <h4>قسم المرور والتراخيص الخمس</h4>
        </div>

        <div class="report-title">
            {% if report_type == 'type' %}
            تقرير الإجازات حسب النوع
            {% endif %}
        </div>

        <div class="table-responsive">
            <table class="table" id="reportTable">
                <thead>
                    <tr>
                        <th colspan="8" class="text-center">
                            {% if report_type == 'type' %}
                            تقرير الإجازات حسب النوع
                            {% endif %}
                            - تاريخ التقرير: {{ now.strftime('%Y-%m-%d') }}
                        </th>
                    </tr>
                </thead>

                {% for group_name, leaves in report_data.items() %}
                <thead>
                    <tr>
                        <th colspan="8" class="table-group-header">{{ group_name }} ({{ leaves|length }})</th>
                    </tr>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>الرتبة</th>
                        <th>الوحدة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in leaves %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ leave.employee.name }}</td>
                        <td>{{ leave.employee.military_rank }}</td>
                        <td>{{ leave.employee.unit }}</td>
                        <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ leave.total_days }}</td>
                        <td>
                            {% if leave.status.name == 'PENDING' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status.name == 'APPROVED' %}
                            <span class="badge bg-success">موافق عليها</span>
                            {% elif leave.status.name == 'REJECTED' %}
                            <span class="badge bg-danger">مرفوضة</span>
                            {% elif leave.status.name == 'CANCELLED' %}
                            <span class="badge bg-secondary">ملغية</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                {% endfor %}

                <tfoot>
                    <tr>
                        <th colspan="2">إجمالي عدد الإجازات:</th>
                        <th colspan="6">
                            {% set total = namespace(value=0) %}
                            {% for group_name, leaves in report_data.items() %}
                                {% set total.value = total.value + leaves|length %}
                            {% endfor %}
                            {{ total.value }}
                        </th>
                    </tr>
                    <tr>
                        <th colspan="2">إجمالي عدد أيام الإجازات:</th>
                        <th colspan="6">
                            {% set total_days = namespace(value=0) %}
                            {% for group_name, leaves in report_data.items() %}
                                {% for leave in leaves %}
                                    {% set total_days.value = total_days.value + leave.total_days %}
                                {% endfor %}
                            {% endfor %}
                            {{ total_days.value }}
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="footer">
            <div class="row">
                <div class="col-6 text-start">
                    <p>قسم المرور والتراخيص الخمس</p>
                    <p>يعتمد</p>
                </div>
                <div class="col-6 text-end">
                    <p>المستخدم: {{ current_user.username }}</p>
                    <p>تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
            </div>
        </div>
        </div> <!-- end of print-container -->
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        function exportTableToExcel(tableID, filename = '') {
            var downloadLink;
            var dataType = 'application/vnd.ms-excel';
            var tableSelect = document.getElementById(tableID);

            // Add UTF-8 BOM for proper Arabic support
            var tableHTML = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Sheet1</x:Name><x:WorksheetOptions><x:DisplayRightToLeft/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body dir="rtl">' + tableSelect.outerHTML + '</body></html>';

            // Specify file name
            filename = filename ? filename + '.xls' : 'excel_data.xls';

            // Create download link element
            downloadLink = document.createElement("a");

            document.body.appendChild(downloadLink);

            if (navigator.msSaveOrOpenBlob) {
                var blob = new Blob(['\ufeff', tableHTML], {
                    type: dataType
                });
                navigator.msSaveOrOpenBlob(blob, filename);
            } else {
                // Create a link to the file
                downloadLink.href = 'data:' + dataType + ';charset=utf-8,' + encodeURIComponent(tableHTML);

                // Setting the file name
                downloadLink.download = filename;

                //triggering the function
                downloadLink.click();
            }

            // Clean up
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>
