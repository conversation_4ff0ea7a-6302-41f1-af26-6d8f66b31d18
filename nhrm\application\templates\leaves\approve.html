{% extends 'layouts/base.html' %}

{% block title %}مراجعة طلب الإجازة - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-check-circle me-2"></i>مراجعة طلب الإجازة #{{ leave_request.id }}
        </h5>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>بيانات الموظف
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">الاسم</th>
                                    <td>{{ employee.name }}</td>
                                </tr>
                                <tr>
                                    <th>الرقم العسكري</th>
                                    <td>{{ employee.military_id }}</td>
                                </tr>
                                <tr>
                                    <th>الرتبة</th>
                                    <td>{{ employee.military_rank }}</td>
                                </tr>
                                <tr>
                                    <th>الوحدة</th>
                                    <td>{{ employee.department_name }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>بيانات الإجازة
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">نوع الإجازة</th>
                                    <td>{{ leave_type.name }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ البداية</th>
                                    <td>{{ leave_request.start_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ النهاية</th>
                                    <td>{{ leave_request.end_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>عدد الأيام</th>
                                    <td>{{ leave_request.total_days }} يوم</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-comment-alt me-2"></i>سبب الإجازة
                </h6>
            </div>
            <div class="card-body">
                <p class="border rounded p-3 bg-light">{{ leave_request.reason or 'لم يتم تحديد سبب' }}</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>قرار المراجعة
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('leaves.approve', id=leave_request.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.status.label(class="form-label") }}
                        {% if form.status.errors %}
                            {{ form.status(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.status(class="form-select") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.comments.label(class="form-label") }}
                        {% if form.comments.errors %}
                            {{ form.comments(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.comments.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.comments(class="form-control", rows=3, placeholder="أدخل ملاحظات المراجعة") }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('leaves.view', id=leave_request.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
