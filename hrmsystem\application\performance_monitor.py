"""
مراقب الأداء - نظام إدارة الموارد البشرية
Performance Monitor - HR Management System

يوفر هذا الملف:
- مراقبة أداء قاعدة البيانات
- تحسين الاستعلامات البطيئة
- مراقبة استخدام الذاكرة
- إحصائيات الأداء
"""

import time
import logging
from functools import wraps
from flask import request, g
from sqlalchemy import event
from sqlalchemy.engine import Engine
from datetime import datetime, timedelta
from collections import defaultdict

# استيراد psutil بشكل اختياري
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """مراقب الأداء الرئيسي"""
    
    def __init__(self, app=None):
        self.app = app
        self.slow_queries = []
        self.request_times = defaultdict(list)
        self.db_query_count = 0
        self.db_query_time = 0
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مراقب الأداء مع التطبيق"""
        self.app = app
        
        # إعداد مراقبة قاعدة البيانات
        self.setup_db_monitoring()
        
        # إعداد مراقبة الطلبات
        self.setup_request_monitoring()
        
        # إضافة دوال المساعدة للقوالب
        app.jinja_env.globals['get_system_stats'] = self.get_system_stats
    
    def setup_db_monitoring(self):
        """إعداد مراقبة أداء قاعدة البيانات"""
        
        @event.listens_for(Engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @event.listens_for(Engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total = time.time() - context._query_start_time
            
            # تسجيل الاستعلامات البطيئة (أكثر من 100ms)
            if total > 0.1:
                self.slow_queries.append({
                    'query': statement[:200] + '...' if len(statement) > 200 else statement,
                    'time': total,
                    'timestamp': datetime.now()
                })
                
                # الاحتفاظ بآخر 50 استعلام بطيء فقط
                if len(self.slow_queries) > 50:
                    self.slow_queries = self.slow_queries[-50:]
                
                logger.warning(f"استعلام بطيء ({total:.3f}s): {statement[:100]}...")
            
            # إحصائيات عامة
            self.db_query_count += 1
            self.db_query_time += total
    
    def setup_request_monitoring(self):
        """إعداد مراقبة أداء الطلبات"""
        
        @self.app.before_request
        def before_request():
            g.start_time = time.time()
            g.db_query_count_start = self.db_query_count
        
        @self.app.after_request
        def after_request(response):
            if hasattr(g, 'start_time'):
                total_time = time.time() - g.start_time
                endpoint = request.endpoint or 'unknown'
                
                # تسجيل الطلبات البطيئة (أكثر من ثانية واحدة)
                if total_time > 1.0:
                    queries_count = self.db_query_count - g.db_query_count_start
                    logger.warning(
                        f"طلب بطيء ({total_time:.3f}s): {endpoint} - "
                        f"استعلامات: {queries_count}"
                    )
                
                # حفظ إحصائيات الطلب
                self.request_times[endpoint].append(total_time)
                
                # الاحتفاظ بآخر 100 طلب لكل endpoint
                if len(self.request_times[endpoint]) > 100:
                    self.request_times[endpoint] = self.request_times[endpoint][-100:]
                
                # إضافة headers للأداء
                response.headers['X-Response-Time'] = f"{total_time:.3f}s"
                response.headers['X-DB-Queries'] = str(self.db_query_count - g.db_query_count_start)
            
            return response
    
    def get_system_stats(self):
        """الحصول على إحصائيات النظام"""
        if not PSUTIL_AVAILABLE:
            return {
                'memory': {'total': 'غير متوفر', 'used': 'غير متوفر', 'percent': 0},
                'cpu': {'percent': 0},
                'disk': {'total': 'غير متوفر', 'used': 'غير متوفر', 'percent': 0}
            }

        try:
            # إحصائيات الذاكرة
            memory = psutil.virtual_memory()

            # إحصائيات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)

            # إحصائيات القرص
            disk = psutil.disk_usage('/')

            return {
                'memory': {
                    'total': self._format_bytes(memory.total),
                    'used': self._format_bytes(memory.used),
                    'percent': memory.percent
                },
                'cpu': {
                    'percent': cpu_percent
                },
                'disk': {
                    'total': self._format_bytes(disk.total),
                    'used': self._format_bytes(disk.used),
                    'percent': (disk.used / disk.total) * 100
                }
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النظام: {e}")
            return {}
    
    def get_performance_report(self):
        """تقرير شامل عن الأداء"""
        report = {
            'timestamp': datetime.now(),
            'system_stats': self.get_system_stats(),
            'database': {
                'total_queries': self.db_query_count,
                'total_time': self.db_query_time,
                'avg_time': self.db_query_time / max(self.db_query_count, 1),
                'slow_queries_count': len(self.slow_queries)
            },
            'requests': {},
            'slow_queries': self.slow_queries[-10:]  # آخر 10 استعلامات بطيئة
        }
        
        # إحصائيات الطلبات
        for endpoint, times in self.request_times.items():
            if times:
                report['requests'][endpoint] = {
                    'count': len(times),
                    'avg_time': sum(times) / len(times),
                    'max_time': max(times),
                    'min_time': min(times)
                }
        
        return report
    
    def _format_bytes(self, bytes_value):
        """تنسيق البايتات إلى وحدة قابلة للقراءة"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"

def performance_timer(func):
    """مُزخرف لقياس وقت تنفيذ الدوال"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            if execution_time > 0.5:  # تسجيل الدوال البطيئة فقط
                logger.info(f"دالة بطيئة: {func.__name__} ({execution_time:.3f}s)")
    return wrapper

def optimize_query_performance():
    """نصائح لتحسين أداء الاستعلامات"""
    tips = [
        "استخدم lazy loading للعلاقات غير المطلوبة فوراً",
        "أضف فهارس للأعمدة المستخدمة في WHERE و ORDER BY",
        "استخدم pagination للنتائج الكبيرة",
        "تجنب N+1 queries باستخدام joinedload",
        "استخدم select_related للعلاقات المطلوبة",
        "قم بتحسين استعلامات COUNT باستخدام exists()",
        "استخدم bulk operations للعمليات الكبيرة",
        "فعل connection pooling لقاعدة البيانات"
    ]
    return tips

# إنشاء مثيل مراقب الأداء
performance_monitor = PerformanceMonitor()
