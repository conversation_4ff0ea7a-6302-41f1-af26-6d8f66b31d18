{% extends 'layouts/base.html' %}

{% block title %}الإشعارات - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .notification-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 15px;
        transition: background-color 0.3s;
    }
    
    .notification-item:hover {
        background-color: #f8f9fa;
    }
    
    .notification-item.unread {
        background-color: rgba(0, 123, 255, 0.05);
    }
    
    .notification-icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .notification-text {
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .notification-time {
        font-size: 0.8rem;
        color: #adb5bd;
    }
    
    .notification-actions {
        margin-left: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-bell me-2"></i>الإشعارات
        </h5>
        <button id="markAllReadBtn" class="btn btn-light btn-sm">
            <i class="fas fa-check-double me-1"></i>تعيين الكل كمقروء
        </button>
    </div>
    <div class="card-body p-0">
        {% if notifications %}
            <div class="list-group list-group-flush">
                {% for notification in notifications %}
                    <div class="list-group-item notification-item {% if not notification.is_read %}unread{% endif %}" data-id="{{ notification.id }}">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon">
                                {% if notification.type == 'leave_approved' %}
                                    <div class="notification-icon-circle bg-success">
                                        <i class="fas fa-check-circle text-white fa-lg"></i>
                                    </div>
                                {% elif notification.type == 'leave_rejected' %}
                                    <div class="notification-icon-circle bg-danger">
                                        <i class="fas fa-times-circle text-white fa-lg"></i>
                                    </div>
                                {% elif notification.type == 'leave_request' %}
                                    <div class="notification-icon-circle bg-info">
                                        <i class="fas fa-calendar-alt text-white fa-lg"></i>
                                    </div>
                                {% elif notification.type == 'system' %}
                                    <div class="notification-icon-circle bg-primary">
                                        <i class="fas fa-info-circle text-white fa-lg"></i>
                                    </div>
                                {% else %}
                                    <div class="notification-icon-circle bg-secondary">
                                        <i class="fas fa-bell text-white fa-lg"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="notification-content">
                                <h6 class="notification-title">{{ notification.title }}</h6>
                                <p class="notification-text mb-1">{{ notification.message }}</p>
                                <div class="notification-time">
                                    <i class="fas fa-clock me-1"></i>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                            <div class="notification-actions">
                                {% if notification.link %}
                                    <a href="{{ notification.link }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                {% endif %}
                                {% if not notification.is_read %}
                                    <button class="btn btn-sm btn-outline-success mark-read-btn" data-id="{{ notification.id }}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                <p class="text-muted">لا توجد إشعارات</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mark single notification as read
        const markReadBtns = document.querySelectorAll('.mark-read-btn');
        markReadBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const notificationId = this.getAttribute('data-id');
                markAsRead(notificationId, this);
            });
        });
        
        // Mark all notifications as read
        const markAllReadBtn = document.getElementById('markAllReadBtn');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', markAllAsRead);
        }
        
        // Function to mark a notification as read
        function markAsRead(id, button) {
            fetch(`/notifications/mark-read/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove unread class from notification
                    const notification = button.closest('.notification-item');
                    notification.classList.remove('unread');
                    
                    // Remove the mark as read button
                    button.remove();
                    
                    // Update notification count in navbar
                    updateNotificationCount();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }
        
        // Function to mark all notifications as read
        function markAllAsRead() {
            fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove unread class from all notifications
                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });
                    
                    // Remove all mark as read buttons
                    document.querySelectorAll('.mark-read-btn').forEach(btn => {
                        btn.remove();
                    });
                    
                    // Update notification count in navbar
                    updateNotificationCount();
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        }
        
        // Function to update notification count in navbar
        function updateNotificationCount() {
            fetch('/notifications/count')
            .then(response => response.json())
            .then(data => {
                const countBadge = document.querySelector('.notification-badge');
                if (countBadge) {
                    if (data.count > 0) {
                        countBadge.textContent = data.count;
                        countBadge.style.display = 'inline-block';
                    } else {
                        countBadge.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error updating notification count:', error);
            });
        }
    });
</script>
{% endblock %}
