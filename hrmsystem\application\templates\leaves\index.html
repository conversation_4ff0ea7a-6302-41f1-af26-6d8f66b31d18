{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar-alt"></i> إدارة الإجازات</h2>
        <div>
            <a href="{{ url_for('leaves.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة طلب إجازة جديد
            </a>
            <a href="{{ url_for('leaves.calendar') }}" class="btn btn-info">
                <i class="fas fa-calendar"></i> عرض التقويم
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter"></i> تصفية النتائج</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('leaves.index') }}">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="PENDING" {% if status == 'PENDING' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="APPROVED" {% if status == 'APPROVED' %}selected{% endif %}>موافق عليها</option>
                            <option value="REJECTED" {% if status == 'REJECTED' %}selected{% endif %}>مرفوضة</option>
                            <option value="CANCELLED" {% if status == 'CANCELLED' %}selected{% endif %}>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="employee_id" class="form-label">الموظف</label>
                        <select name="employee_id" id="employee_id" class="form-select">
                            <option value="">جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if employee_id == employee.id %}selected{% endif %}>{{ employee.name }} ({{ employee.military_id }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="leave_type_id" class="form-label">نوع الإجازة</label>
                        <select name="leave_type_id" id="leave_type_id" class="form-select">
                            <option value="">جميع الأنواع</option>
                            {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}" {% if leave_type_id == leave_type.id %}selected{% endif %}>{{ leave_type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">تصفية</button>
                    <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Leaves List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> قائمة طلبات الإجازة</h5>
        </div>
        <div class="card-body">
            {% if leaves.items %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leaves.items %}
                        <tr>
                            <td>{{ leave.employee.name }}</td>
                            <td>
                                <span class="badge" style="background-color: {{ leave.leave_type_rel.color }}">{{ leave.leave_type_rel.name }}</span>
                            </td>
                            <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave.total_days }}</td>
                            <td>
                                {% if leave.status.name == 'PENDING' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif leave.status.name == 'APPROVED' %}
                                <span class="badge bg-success">موافق عليها</span>
                                {% elif leave.status.name == 'REJECTED' %}
                                <span class="badge bg-danger">مرفوضة</span>
                                {% elif leave.status.name == 'CANCELLED' %}
                                <span class="badge bg-secondary">ملغية</span>
                                {% endif %}
                            </td>
                            <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('leaves.view', id=leave.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if leave.status.name == 'PENDING' %}
                                    <a href="{{ url_for('leaves.edit', id=leave.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ leave.id }}" data-bs-toggle="tooltip" title="موافقة">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ leave.id }}" data-bs-toggle="tooltip" title="رفض">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    {% if leave.status.name != 'CANCELLED' %}
                                    <button type="button" class="btn btn-sm btn-secondary" data-bs-toggle="modal" data-bs-target="#cancelModal{{ leave.id }}" data-bs-toggle="tooltip" title="إلغاء">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ leave.id }}" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Approve Modal -->
                                <div class="modal fade" id="approveModal{{ leave.id }}" tabindex="-1" aria-labelledby="approveModalLabel{{ leave.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="approveModalLabel{{ leave.id }}">تأكيد الموافقة</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form action="{{ url_for('leaves.approve', id=leave.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <div class="modal-body">
                                                    <p>هل أنت متأكد من الموافقة على طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="deduct_from_balance" value="true" id="deductFromBalance{{ leave.id }}" checked>
                                                        <label class="form-check-label" for="deductFromBalance{{ leave.id }}">
                                                            خصم من رصيد الإجازة ({{ leave.employee.leave_balance }} يوم)
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-success">موافقة</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reject Modal -->
                                <div class="modal fade" id="rejectModal{{ leave.id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ leave.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="rejectModalLabel{{ leave.id }}">تأكيد الرفض</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form action="{{ url_for('leaves.reject', id=leave.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <div class="modal-body">
                                                    <p>هل أنت متأكد من رفض طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                                                    <div class="mb-3">
                                                        <label for="rejectionReason{{ leave.id }}" class="form-label">سبب الرفض</label>
                                                        <textarea class="form-control" id="rejectionReason{{ leave.id }}" name="rejection_reason" rows="3"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-danger">رفض</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cancel Modal -->
                                <div class="modal fade" id="cancelModal{{ leave.id }}" tabindex="-1" aria-labelledby="cancelModalLabel{{ leave.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="cancelModalLabel{{ leave.id }}">تأكيد الإلغاء</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form action="{{ url_for('leaves.cancel', id=leave.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <div class="modal-body">
                                                    <p>هل أنت متأكد من إلغاء طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                                                    {% if leave.status.name == 'APPROVED' %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="restore_balance" value="true" id="restoreBalance{{ leave.id }}" checked>
                                                        <label class="form-check-label" for="restoreBalance{{ leave.id }}">
                                                            استعادة رصيد الإجازة ({{ leave.total_days }} يوم)
                                                        </label>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-warning">إلغاء الطلب</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ leave.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ leave.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ leave.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('leaves.delete', id=leave.id) }}" method="POST">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if leaves.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('leaves.index', page=leaves.prev_num, status=status, employee_id=employee_id, leave_type_id=leave_type_id) }}">السابق</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    {% endif %}

                    {% for page_num in leaves.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == leaves.page %}
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="{{ url_for('leaves.index', page=page_num, status=status, employee_id=employee_id, leave_type_id=leave_type_id) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('leaves.index', page=page_num, status=status, employee_id=employee_id, leave_type_id=leave_type_id) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if leaves.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('leaves.index', page=leaves.next_num, status=status, employee_id=employee_id, leave_type_id=leave_type_id) }}">التالي</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا توجد طلبات إجازة متطابقة مع معايير البحث.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
