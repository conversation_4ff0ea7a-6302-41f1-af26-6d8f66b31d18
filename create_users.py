from application import create_app, db
from application.models import User
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone

app = create_app()

with app.app_context():
    # Create tables
    db.create_all()

    # Check if admin user exists
    admin_exists = User.query.filter_by(username="admin").first()
    if not admin_exists:
        print("Creating admin user...")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password=generate_password_hash("admin123"),
            role="admin",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(admin_user)
    else:
        print("Admin user already exists.")

    # Check if regular user exists
    user_exists = User.query.filter_by(username="user").first()
    if not user_exists:
        print("Creating regular user...")
        regular_user = User(
            username="user",
            email="<EMAIL>",
            password=generate_password_hash("user123"),
            role="employee",
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(regular_user)
    else:
        print("Regular user already exists.")

    # Commit changes
    db.session.commit()

    # Verify users
    users = User.query.all()
    print("Users in database:")
    for user in users:
        print(f"ID: {user.id}, Username: {user.username}, Email: {user.email}, Role: {user.role}, Active: {user.is_active}")

    # Test password verification
    admin = User.query.filter_by(username="admin").first()
    if admin:
        test_password = "admin123"
        is_password_correct = check_password_hash(admin.password, test_password)
        print(f"\nPassword verification test for admin:")
        print(f"Password: {test_password}")
        print(f"Stored hash: {admin.password}")
        print(f"Verification result: {is_password_correct}")
