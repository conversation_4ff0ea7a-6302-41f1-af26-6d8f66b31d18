@echo off
title تشغيل نظام إدارة الموارد البشرية
cd /d "%~dp0"

:: تحديد وضع التشغيل (الإنتاج أو التطوير)
set /p mode="اختر وضع التشغيل (1 للإنتاج، 2 للتطوير): "

:: الحصول على عنوان IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found_ip
)
:found_ip
set IP=%IP:~1%

if "%mode%"=="1" (
    :: وضع الإنتاج (المنفذ 8080)
    echo.
    echo ===================================================
    echo نظام إدارة الموارد البشرية يعمل الآن في وضع الإنتاج
    echo يمكنك الوصول إلى النظام من خلال:
    echo http://%IP%:8080
    echo ===================================================
    echo.
    echo انتظر 7 ثوانٍ لبدء تشغيل المتصفح...

    :: تشغيل ملف run_production.py
    start "" python run_production.py

    :: انتظر 7 ثوانٍ لضمان تشغيل الخادم
    timeout /t 7 /nobreak >nul

    :: افتح المتصفح على عنوان IP المحلي
    start "" "http://%IP%:8080/"
) else (
    :: وضع التطوير (المنفذ 5000)
    echo.
    echo ===================================================
    echo نظام إدارة الموارد البشرية يعمل الآن في وضع التطوير
    echo يمكنك الوصول إلى النظام من خلال:
    echo http://%IP%:5000
    echo ===================================================
    echo.
    echo انتظر 7 ثوانٍ لبدء تشغيل المتصفح...

    :: تشغيل ملف run.py
    start "" python run.py

    :: انتظر 7 ثوانٍ لضمان تشغيل الخادم
    timeout /t 7 /nobreak >nul

    :: افتح المتصفح على عنوان IP المحلي
    start "" "http://%IP%:5000/"
)

:: لا تغلق نافذة CMD حتى يتمكن المستخدم من رؤية معلومات الاتصال
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
exit
