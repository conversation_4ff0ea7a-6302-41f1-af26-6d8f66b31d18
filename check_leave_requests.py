import sqlite3

def check_leave_requests():
    """التحقق من طلبات الإجازة في قاعدة البيانات"""
    try:
        # اتصال مباشر بقاعدة البيانات SQLite
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # عرض هيكل جدول طلبات الإجازة
        cursor.execute("PRAGMA table_info(leave_request)")
        columns = cursor.fetchall()
        print("هيكل جدول leave_request:")
        for column in columns:
            print(f"  {column[1]} ({column[2]}) {'NOT NULL' if column[3] else 'NULL'} {'PRIMARY KEY' if column[5] else ''}")
        
        # عرض بيانات طلبات الإجازة
        cursor.execute("SELECT * FROM leave_request")
        leave_requests = cursor.fetchall()
        
        if leave_requests:
            # الحصول على أسماء الأعمدة
            column_names = [column[1] for column in columns]
            
            print("\nبيانات طلبات الإجازة:")
            for leave_request in leave_requests:
                print("\nطلب إجازة:")
                for i, value in enumerate(leave_request):
                    print(f"  {column_names[i]}: {value}")
        else:
            print("\nلا توجد طلبات إجازة في قاعدة البيانات")
        
        conn.close()
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    check_leave_requests()
