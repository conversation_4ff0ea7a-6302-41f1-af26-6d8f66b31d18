# نظام إدارة الموارد البشرية (HRM)

نظام إدارة الموارد البشرية هو تطبيق ويب مصمم لإدارة موظفي المؤسسة العسكرية، بما في ذلك معلوماتهم الشخصية، طلبات الإجازة، والإعدادات النظامية. يوفر النظام واجهة سهلة الاستخدام للمسؤولين والمستخدمين لإدارة البيانات وتتبع الأنشطة.

## المميزات الرئيسية

- إدارة بيانات الموظفين (الاسم، الرقم العسكري، الرقم الوطني، فصيلة الدم، إلخ)
- إدارة طلبات الإجازة مع حساب تلقائي للأيام واستثناء العطلات وعطلات نهاية الأسبوع
- لوحة تحكم تفاعلية مع رسوم بيانية وإحصائيات
- تقارير شاملة عن الموظفين حسب الرتبة والوحدة والحالة
- استيراد وتصدير بيانات الموظفين
- طباعة بيانات الموظفين
- تقويم تفاعلي للإجازات
- رمز QR لعرض بيانات الموظفين
- سجلات تدقيق لتتبع جميع التغييرات في النظام

## متطلبات النظام

- Python 3.8+
- Flask 2.3.3+
- MySQL 5.7+ أو SQLite
- المكتبات المذكورة في ملف requirements.txt

## التثبيت

1. قم بتنزيل أو استنساخ المشروع:
```
git clone https://github.com/yourusername/hrmsystem.git
cd hrmsystem
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

3. قم بتثبيت المكتبات المطلوبة:
```
pip install -r requirements.txt
```

4. قم بتشغيل التطبيق:
```
python run.py
```

أو استخدم ملف التشغيل السريع:
```
start_hrm.bat  # للتطوير
start_hrm_production.bat  # للإنتاج
```

5. افتح المتصفح على العنوان:
```
http://127.0.0.1:5000/  # للتطوير
http://YOUR_IP:8080/  # للإنتاج
```

## بيانات الدخول الافتراضية

- اسم المستخدم: admin
- كلمة المرور: admin123

## هيكل المشروع

```
/hrmsystem 
│── /application 
│   ├── __init__.py 
│   ├── auth/ 
│   │   ├── routes.py 
│   │   └── forms.py 
│   ├── employees/ 
│   │   ├── routes.py 
│   │   └── forms.py 
│   ├── leaves/ 
│   │   ├── routes.py 
│   │   └── forms.py 
│   ├── static/ 
│   │   ├── css/ 
│   │   │   ├── auth.css 
│   │   │   ├── main.css 
│   │   │   └── bootstrap.min.css 
│   │   ├── js/ 
│   │   │   ├── auth.js 
│   │   │   └── main.js 
│   │   └── img/ 
│   │       ├── logo.png 
│   │       └── bg-auth.jpg 
│   ├── templates/ 
│   │   ├── auth/ 
│   │   ├── employees/ 
│   │   ├── leaves/ 
│   │   ├── layouts/ 
│   │   └── dashboard/ 
│   ├── models.py 
│   └── utilities.py 
│── config.py 
│── requirements.txt 
│── run.py 
│── run_production.py
│── start_hrm.bat
│── start_hrm_production.bat
└── README.md
```

## المساهمة

نرحب بالمساهمات! يرجى إرسال طلب سحب أو فتح مشكلة للمناقشة.

## الترخيص

هذا المشروع مرخص بموجب [ترخيص MIT](LICENSE).
