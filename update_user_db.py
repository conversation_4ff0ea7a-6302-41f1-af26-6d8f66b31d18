from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os
import sqlite3
import enum

# تعريف الأنواع المستخدمة في قاعدة البيانات
class UserRole(enum.Enum):
    ADMIN = 'مدير النظام'
    SUPERVISOR = 'مشرف'
    USER = 'مستخدم'

class UserStatus(enum.Enum):
    ACTIVE = 'نشط'
    INACTIVE = 'غير نشط'
    SUSPENDED = 'موقوف'

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# تحديث قاعدة البيانات
def update_database(db_path):
    try:
        # إضافة الأعمدة الجديدة باستخدام SQLite مباشرة
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول user
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
        if cursor.fetchone():
            print(f"جدول user موجود في {db_path}")
            
            # التحقق من وجود الأعمدة الجديدة
            cursor.execute("PRAGMA table_info(user)")
            columns = [row[1] for row in cursor.fetchall()]
            print(f"الأعمدة الموجودة في جدول user: {columns}")
            
            # إضافة الأعمدة الجديدة إذا لم تكن موجودة
            if 'full_name' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN full_name TEXT")
                print(f"تم إضافة عمود full_name إلى {db_path}")
                
            if 'role' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN role TEXT")
                print(f"تم إضافة عمود role إلى {db_path}")
                
            if 'status' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN status TEXT")
                print(f"تم إضافة عمود status إلى {db_path}")
                
            if 'phone' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN phone TEXT")
                print(f"تم إضافة عمود phone إلى {db_path}")
                
            if 'profile_image' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN profile_image TEXT")
                print(f"تم إضافة عمود profile_image إلى {db_path}")
                
            if 'last_login' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN last_login TIMESTAMP")
                print(f"تم إضافة عمود last_login إلى {db_path}")
                
            # تحديث قيم الأعمدة الجديدة
            cursor.execute("UPDATE user SET role = ? WHERE is_admin = 1", (UserRole.ADMIN.name,))
            cursor.execute("UPDATE user SET role = ? WHERE is_admin = 0 OR is_admin IS NULL", (UserRole.USER.name,))
            cursor.execute("UPDATE user SET status = ?", (UserStatus.ACTIVE.name,))
            print(f"تم تحديث قيم الأعمدة الجديدة في {db_path}")
            
            # إنشاء جدول user_audit_log إذا لم يكن موجودًا
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action_by INTEGER NOT NULL,
                action TEXT NOT NULL,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (action_by) REFERENCES user (id)
            )
            """)
            print(f"تم إنشاء جدول user_audit_log في {db_path}")
            
            conn.commit()
            print(f"تم تحديث قاعدة البيانات {db_path} بنجاح")
        else:
            print(f"جدول user غير موجود في {db_path}")
            
        conn.close()
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات {db_path}: {e}")

# التحقق من وجود ملفات قواعد البيانات
db_files = ['app.db', 'instance/app.db', 'instance/hrm.db']
for db_file in db_files:
    if os.path.exists(db_file):
        print(f"قاعدة البيانات موجودة: {db_file}")
        update_database(db_file)
    else:
        print(f"قاعدة البيانات غير موجودة: {db_file}")

print("تم الانتهاء من تحديث قواعد البيانات")
