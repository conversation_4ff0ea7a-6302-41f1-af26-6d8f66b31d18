from flask import Blueprint, render_template, jsonify, request, flash, redirect, url_for
from flask_login import login_required, current_user
from werkzeug.security import check_password_hash
from ..models import Employee, LeaveRequest, LeaveStatus, EmployeeCategory, EmployeeStatus, User, AuditLog
from ..decorators import admin_required
from .. import db
from sqlalchemy import func
from datetime import datetime, timedelta
import os

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get counts for dashboard cards
    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status=EmployeeStatus.ACTIVE).count()
    on_leave_employees = Employee.query.filter_by(status=EmployeeStatus.ON_LEAVE).count()
    pending_leaves = LeaveRequest.query.filter_by(status=LeaveStatus.PENDING).count()

    # Get recent leave requests
    recent_leaves = LeaveRequest.query.order_by(LeaveRequest.created_at.desc()).limit(5).all()

    # Get employees by category for chart
    employees_by_category = db.session.query(
        Employee.category,
        func.count(Employee.id)
    ).group_by(Employee.category).all()

    category_labels = [cat[0].value if cat[0] else 'غير محدد' for cat in employees_by_category]
    category_data = [cat[1] for cat in employees_by_category]

    # Get employees by status for chart
    employees_by_status = db.session.query(
        Employee.status,
        func.count(Employee.id)
    ).group_by(Employee.status).all()

    status_labels = [stat[0].value if stat[0] else 'غير محدد' for stat in employees_by_status]
    status_data = [stat[1] for stat in employees_by_status]

    # Get employees by unit for chart
    employees_by_unit = db.session.query(
        Employee.unit,
        func.count(Employee.id)
    ).group_by(Employee.unit).order_by(func.count(Employee.id).desc()).limit(5).all()

    unit_labels = [unit[0] if unit[0] else 'غير محدد' for unit in employees_by_unit]
    unit_data = [unit[1] for unit in employees_by_unit]

    # Get employees by rank for chart
    employees_by_rank = db.session.query(
        Employee.military_rank,
        func.count(Employee.id)
    ).group_by(Employee.military_rank).order_by(func.count(Employee.id).desc()).limit(5).all()

    rank_labels = [rank[0] if rank[0] else 'غير محدد' for rank in employees_by_rank]
    rank_data = [rank[1] for rank in employees_by_rank]

    return render_template('dashboard/index.html',
                           total_employees=total_employees,
                           active_employees=active_employees,
                           on_leave_employees=on_leave_employees,
                           pending_leaves=pending_leaves,
                           recent_leaves=recent_leaves,
                           category_labels=category_labels,
                           category_data=category_data,
                           status_labels=status_labels,
                           status_data=status_data,
                           unit_labels=unit_labels,
                           unit_data=unit_data,
                           rank_labels=rank_labels,
                           rank_data=rank_data,
                           now=datetime.now(),
                           title='لوحة التحكم')

@dashboard_bp.route('/chart_data')
@login_required
def chart_data():
    chart_type = request.args.get('type', 'category')

    if chart_type == 'category':
        # Get employees by category
        employees_by_category = db.session.query(
            Employee.category,
            func.count(Employee.id)
        ).group_by(Employee.category).all()

        labels = [cat[0].value if cat[0] else 'غير محدد' for cat in employees_by_category]
        data = [cat[1] for cat in employees_by_category]

    elif chart_type == 'status':
        # Get employees by status
        employees_by_status = db.session.query(
            Employee.status,
            func.count(Employee.id)
        ).group_by(Employee.status).all()

        labels = [stat[0].value if stat[0] else 'غير محدد' for stat in employees_by_status]
        data = [stat[1] for stat in employees_by_status]

    elif chart_type == 'unit':
        # Get employees by unit
        employees_by_unit = db.session.query(
            Employee.unit,
            func.count(Employee.id)
        ).group_by(Employee.unit).order_by(func.count(Employee.id).desc()).limit(10).all()

        labels = [unit[0] if unit[0] else 'غير محدد' for unit in employees_by_unit]
        data = [unit[1] for unit in employees_by_unit]

    elif chart_type == 'rank':
        # Get employees by rank
        employees_by_rank = db.session.query(
            Employee.military_rank,
            func.count(Employee.id)
        ).group_by(Employee.military_rank).order_by(func.count(Employee.id).desc()).limit(10).all()

        labels = [rank[0] if rank[0] else 'غير محدد' for rank in employees_by_rank]
        data = [rank[1] for rank in employees_by_rank]

    else:
        labels = []
        data = []

    return jsonify({
        'labels': labels,
        'data': data
    })

@dashboard_bp.route('/reset_database', methods=['GET', 'POST'])
@login_required
@admin_required
def reset_database():
    """صفحة إعادة تعيين قاعدة البيانات - للمديرين فقط"""
    if request.method == 'GET':
        return render_template('dashboard/reset_database.html', title='إعادة تعيين قاعدة البيانات')

    # التحقق من كلمة المرور
    password = request.form.get('password')
    confirmation = request.form.get('confirmation')

    if not password:
        flash('يرجى إدخال كلمة المرور للتأكيد', 'danger')
        return redirect(url_for('dashboard.reset_database'))

    if not check_password_hash(current_user.password, password):
        # تسجيل محاولة إعادة تعيين فاشلة
        try:
            log = AuditLog(
                user_id=current_user.id,
                action='database_reset_failed',
                entity='System',
                entity_id=None,
                details=f'محاولة إعادة تعيين قاعدة البيانات بكلمة مرور خاطئة من المستخدم {current_user.username}',
                ip_address=request.remote_addr
            )
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            print(f"Error creating audit log: {e}")

        flash('كلمة المرور غير صحيحة', 'danger')
        return redirect(url_for('dashboard.reset_database'))

    if confirmation != 'RESET_DATABASE':
        flash('يرجى كتابة "RESET_DATABASE" للتأكيد', 'danger')
        return redirect(url_for('dashboard.reset_database'))

    # تسجيل محاولة إعادة التعيين
    try:
        log = AuditLog(
            user_id=current_user.id,
            action='database_reset_attempt',
            entity='System',
            entity_id=None,
            details=f'بدء عملية إعادة تعيين قاعدة البيانات من المستخدم {current_user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        print(f"Error creating audit log: {e}")

    # تنفيذ إعادة التعيين
    try:
        from ..utilities import reset_database_secure
        success = reset_database_secure()

        if success:
            flash('تم إعادة تعيين قاعدة البيانات بنجاح', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('حدث خطأ أثناء إعادة تعيين قاعدة البيانات', 'danger')
            return redirect(url_for('dashboard.reset_database'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إعادة تعيين قاعدة البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard.reset_database'))
