{% extends 'layouts/base.html' %}

{% block title %}لوحة التحكم - نظام إدارة الموارد البشرية{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .stat-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
    }

    .stat-label {
        font-size: 1rem;
        opacity: 0.8;
    }

    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="80" height="80" class="mb-3">
                    <h2>مرحباً بك في نظام إدارة الموارد البشرية</h2>
                </div>
                <p class="text-center">نظام متكامل لإدارة الموظفين والإجازات في قسم المرور والتراخيص الخمس </p>
                <div class="row mt-4">
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-primary text-white rounded-circle p-3">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">إجمالي الموظفين</h6>
                                <h4 class="mb-0">{{ stats.employee_count }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-success text-white rounded-circle p-3">
                                    <i class="fas fa-building"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">عدد الوحدات</h6>
                                <h4 class="mb-0">{{ stats.department_count }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-info text-white rounded-circle p-3">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">طلبات الإجازة</h6>
                                <h4 class="mb-0">{{ stats.pending_leaves }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-warning text-dark rounded-circle p-3">
                                    <i class="fas fa-user-slash"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">غياب وهروب</h6>
                                <h4 class="mb-0">{{ stats.absent_count }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.employee_count }}</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-light mt-3">عرض الموظفين</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.officers_count }}</div>
                        <div class="stat-label">الضباط</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-light mt-3">عرض الضباط</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.ncos_count }}</div>
                        <div class="stat-label">ضباط الصف</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-light mt-3">عرض ضباط الصف</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-secondary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.employees_count }}</div>
                        <div class="stat-label">الموظفين المدنيين</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-light mt-3">عرض الموظفين المدنيين</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.on_leave_count }}</div>
                        <div class="stat-label">في إجازة</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-dark mt-3">عرض الموظفين في إجازة</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.absent_count }}</div>
                        <div class="stat-label">غياب وهروب</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-user-slash"></i>
                    </div>
                </div>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-light mt-3">عرض الغائبين</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.pending_leaves }}</div>
                        <div class="stat-label">طلبات الإجازة المعلقة</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <a href="{{ url_for('leaves.index') }}?status=pending" class="btn btn-sm btn-outline-light mt-3">عرض الطلبات</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-value">{{ stats.approved_leaves }}</div>
                        <div class="stat-label">الإجازات المعتمدة</div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <a href="{{ url_for('leaves.index') }}?status=approved" class="btn btn-sm btn-outline-light mt-3">عرض الإجازات</a>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">توزيع الموظفين حسب الرتبة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="rankChart"></canvas>
                </div>
            </div>
            <div class="card-footer bg-light text-center">
                <a href="{{ url_for('reports.employees_by_rank') }}" class="btn btn-sm btn-primary">عرض التقرير الكامل</a>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">توزيع الموظفين حسب الوحدة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="deptChart"></canvas>
                </div>
            </div>
            <div class="card-footer bg-light text-center">
                <a href="{{ url_for('reports.employees_by_unit') }}" class="btn btn-sm btn-primary">عرض التقرير الكامل</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">توزيع الموظفين حسب الحالة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
            <div class="card-footer bg-light text-center">
                <a href="{{ url_for('reports.employees_by_status') }}" class="btn btn-sm btn-primary">عرض التقرير الكامل</a>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">طلبات الإجازة حسب الشهر</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="leaveChart"></canvas>
                </div>
            </div>
            <div class="card-footer bg-light text-center">
                <a href="{{ url_for('reports.leave_requests') }}" class="btn btn-sm btn-primary">عرض تقرير الإجازات</a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Leave Requests -->
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أحدث طلبات الإجازة</h5>
                <a href="{{ url_for('leaves.index') }}" class="btn btn-sm btn-primary">عرض جميع الطلبات</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>من تاريخ</th>
                                <th>إلى تاريخ</th>
                                <th>عدد الأيام</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave, employee in recent_leaves %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>{{ leave.leave_type_rel.name }}</td>
                                <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ leave.total_days }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif leave.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوضة</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ leave.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Rank Chart
        const rankCtx = document.getElementById('rankChart').getContext('2d');
        new Chart(rankCtx, {
            type: 'bar',
            data: {
                labels: {{ rank_names|tojson }},
                datasets: [{
                    label: 'عدد الموظفين',
                    data: {{ rank_values|tojson }},
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Department Chart
        const deptCtx = document.getElementById('deptChart').getContext('2d');
        new Chart(deptCtx, {
            type: 'pie',
            data: {
                labels: {{ dept_names|tojson }},
                datasets: [{
                    data: {{ dept_values|tojson }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)',
                        'rgba(83, 102, 255, 0.7)',
                        'rgba(40, 159, 64, 0.7)',
                        'rgba(210, 199, 199, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)',
                        'rgba(83, 102, 255, 1)',
                        'rgba(40, 159, 64, 1)',
                        'rgba(210, 199, 199, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusLabels = [];
        const statusData = [];
        const statusColors = {
            'مستمر': '#28a745',
            'غياب وهروب': '#dc3545',
            'مكلف': '#007bff',
            'اجازة': '#17a2b8',
            'ايقاف عن العمل': '#ffc107',
            'متفرق': '#6c757d',
            'عيادة طبية': '#20c997'
        };
        const colors = [];

        {% for status, count in status_counts %}
            statusLabels.push('{{ status }}');
            statusData.push({{ count }});
            colors.push(statusColors['{{ status }}'] || '#6c757d');
        {% endfor %}

        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: statusLabels,
                datasets: [{
                    data: statusData,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace(')', ', 0.8)')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Leave Chart
        const leaveCtx = document.getElementById('leaveChart').getContext('2d');
        new Chart(leaveCtx, {
            type: 'line',
            data: {
                labels: {{ month_names|tojson }},
                datasets: [
                    {
                        label: 'معتمدة',
                        data: {{ approved_data|tojson }},
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'مرفوضة',
                        data: {{ rejected_data|tojson }},
                        backgroundColor: 'rgba(220, 53, 69, 0.2)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'معلقة',
                        data: {{ pending_data|tojson }},
                        backgroundColor: 'rgba(255, 193, 7, 0.2)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}
