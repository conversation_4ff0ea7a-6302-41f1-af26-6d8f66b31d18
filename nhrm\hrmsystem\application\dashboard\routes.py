from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from ..models import Employee, LeaveRequest, LeaveStatus, EmployeeCategory, EmployeeStatus
from .. import db
from sqlalchemy import func
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get counts for dashboard cards
    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status=EmployeeStatus.ACTIVE).count()
    on_leave_employees = Employee.query.filter_by(status=EmployeeStatus.ON_LEAVE).count()
    pending_leaves = LeaveRequest.query.filter_by(status=LeaveStatus.PENDING).count()

    # Get recent leave requests
    recent_leaves = LeaveRequest.query.order_by(LeaveRequest.created_at.desc()).limit(5).all()

    # Get employees by category for chart
    employees_by_category = db.session.query(
        Employee.category,
        func.count(Employee.id)
    ).group_by(Employee.category).all()

    category_labels = [cat[0].value if cat[0] else 'غير محدد' for cat in employees_by_category]
    category_data = [cat[1] for cat in employees_by_category]

    # Get employees by status for chart
    employees_by_status = db.session.query(
        Employee.status,
        func.count(Employee.id)
    ).group_by(Employee.status).all()

    status_labels = [stat[0].value if stat[0] else 'غير محدد' for stat in employees_by_status]
    status_data = [stat[1] for stat in employees_by_status]

    # Get employees by unit for chart
    employees_by_unit = db.session.query(
        Employee.unit,
        func.count(Employee.id)
    ).group_by(Employee.unit).order_by(func.count(Employee.id).desc()).limit(5).all()

    unit_labels = [unit[0] if unit[0] else 'غير محدد' for unit in employees_by_unit]
    unit_data = [unit[1] for unit in employees_by_unit]

    # Get employees by rank for chart
    employees_by_rank = db.session.query(
        Employee.military_rank,
        func.count(Employee.id)
    ).group_by(Employee.military_rank).order_by(func.count(Employee.id).desc()).limit(5).all()

    rank_labels = [rank[0] if rank[0] else 'غير محدد' for rank in employees_by_rank]
    rank_data = [rank[1] for rank in employees_by_rank]

    return render_template('dashboard/index.html',
                           total_employees=total_employees,
                           active_employees=active_employees,
                           on_leave_employees=on_leave_employees,
                           pending_leaves=pending_leaves,
                           recent_leaves=recent_leaves,
                           category_labels=category_labels,
                           category_data=category_data,
                           status_labels=status_labels,
                           status_data=status_data,
                           unit_labels=unit_labels,
                           unit_data=unit_data,
                           rank_labels=rank_labels,
                           rank_data=rank_data,
                           now=datetime.now(),
                           title='لوحة التحكم')

@dashboard_bp.route('/chart_data')
@login_required
def chart_data():
    chart_type = request.args.get('type', 'category')

    if chart_type == 'category':
        # Get employees by category
        employees_by_category = db.session.query(
            Employee.category,
            func.count(Employee.id)
        ).group_by(Employee.category).all()

        labels = [cat[0].value if cat[0] else 'غير محدد' for cat in employees_by_category]
        data = [cat[1] for cat in employees_by_category]

    elif chart_type == 'status':
        # Get employees by status
        employees_by_status = db.session.query(
            Employee.status,
            func.count(Employee.id)
        ).group_by(Employee.status).all()

        labels = [stat[0].value if stat[0] else 'غير محدد' for stat in employees_by_status]
        data = [stat[1] for stat in employees_by_status]

    elif chart_type == 'unit':
        # Get employees by unit
        employees_by_unit = db.session.query(
            Employee.unit,
            func.count(Employee.id)
        ).group_by(Employee.unit).order_by(func.count(Employee.id).desc()).limit(10).all()

        labels = [unit[0] if unit[0] else 'غير محدد' for unit in employees_by_unit]
        data = [unit[1] for unit in employees_by_unit]

    elif chart_type == 'rank':
        # Get employees by rank
        employees_by_rank = db.session.query(
            Employee.military_rank,
            func.count(Employee.id)
        ).group_by(Employee.military_rank).order_by(func.count(Employee.id).desc()).limit(10).all()

        labels = [rank[0] if rank[0] else 'غير محدد' for rank in employees_by_rank]
        data = [rank[1] for rank in employees_by_rank]

    else:
        labels = []
        data = []

    return jsonify({
        'labels': labels,
        'data': data
    })
