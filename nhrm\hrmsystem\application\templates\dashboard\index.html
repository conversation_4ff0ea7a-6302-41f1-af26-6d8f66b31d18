{% extends 'layouts/base.html' %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
<style>
    /* Custom Dashboard Styles */
    .dashboard-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('{{ url_for("static", filename="img/pattern.png") }}');
        opacity: 0.1;
    }

    .dashboard-header h2 {
        font-weight: 700;
        margin-bottom: 10px;
        position: relative;
    }

    .dashboard-header p {
        opacity: 0.9;
        font-size: 1.1rem;
        position: relative;
    }

    .dashboard-header .date-time {
        position: relative;
        font-size: 1.1rem;
        margin-top: 15px;
    }

    .stat-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .stat-card .card-body {
        padding: 25px;
    }

    .stat-card .card-footer {
        background: rgba(0, 0, 0, 0.05);
        border-top: none;
        padding: 15px 25px;
    }

    .stat-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .stat-card h2 {
        font-size: 2.5rem;
        font-weight: 700;
    }

    .stat-card i {
        opacity: 0.7;
    }

    .chart-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .chart-card .card-header {
        padding: 20px 25px;
        border-bottom: none;
    }

    .chart-card .card-body {
        padding: 25px;
    }

    .leaves-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .leaves-card .card-header {
        padding: 20px 25px;
        border-bottom: none;
    }

    .leaves-card .card-body {
        padding: 25px;
    }

    .leaves-card .card-footer {
        background: transparent;
        border-top: none;
        padding: 15px 25px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
    }

    .btn-action {
        border-radius: 50px;
        padding: 0.375rem 1rem;
    }

    .btn-action i {
        margin-right: 5px;
    }

    /* Report cards styles */
    .border-primary {
        border-width: 2px;
        transition: all 0.3s ease;
    }

    .border-primary:hover,
    .border-success:hover,
    .border-info:hover,
    .border-warning:hover,
    .border-danger:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .card .btn {
        border-radius: 50px;
        padding: 8px 20px;
        transition: all 0.3s ease;
    }

    .card .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .card .btn i {
        margin-right: 5px;
    }

    /* Custom gradient backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-header text-center">
                <div class="row align-items-center">
                    <div class="col-md-2 text-md-start">
                        <img src="{{ url_for('static', filename='img/hrm_logo.svg') }}" alt="Logo" width="100" height="100" class="mb-3 mb-md-0">
                    </div>
                    <div class="col-md-8">
                        <h2>مرحباً بك في نظام إدارة الموارد البشرية</h2>
                        <p>لوحة التحكم الرئيسية لإدارة بيانات الموظفين والإجازات</p>
                    </div>
                    <div class="col-md-2 text-md-end">
                        <div class="date-time">
                            <div>{{ now.strftime('%Y-%m-%d') }}</div>
                            <div>{{ now.strftime('%H:%M:%S') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي الموظفين</h6>
                            <h2 class="mb-0">{{ total_employees }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{{ url_for('employees.index') }}" class="text-white text-decoration-none d-flex align-items-center w-100 justify-content-between">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الموظفين المستمرين</h6>
                            <h2 class="mb-0">{{ active_employees }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{{ url_for('employees.index', status='ACTIVE') }}" class="text-white text-decoration-none d-flex align-items-center w-100 justify-content-between">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الموظفين في إجازة</h6>
                            <h2 class="mb-0">{{ on_leave_employees }}</h2>
                        </div>
                        <div class="icon-circle bg-dark bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-user-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{{ url_for('employees.index', status='ON_LEAVE') }}" class="text-dark text-decoration-none d-flex align-items-center w-100 justify-content-between">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stat-card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">طلبات الإجازة المعلقة</h6>
                            <h2 class="mb-0">{{ pending_leaves }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-calendar-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{{ url_for('leaves.index', status='PENDING') }}" class="text-white text-decoration-none d-flex align-items-center w-100 justify-content-between">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card chart-card h-100">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">توزيع الموظفين حسب الفئة</h5>
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card chart-card h-100">
                <div class="card-header bg-gradient-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">توزيع الموظفين حسب الحالة</h5>
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card chart-card h-100">
                <div class="card-header bg-gradient-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">توزيع الموظفين حسب الوحدة</h5>
                        <i class="fas fa-chart-bar"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="unitChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card chart-card h-100">
                <div class="card-header bg-gradient-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">توزيع الموظفين حسب الرتبة</h5>
                        <i class="fas fa-chart-bar"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="rankChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card leaves-card">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">التقارير</h5>
                        <i class="fas fa-file-alt"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">تقارير حسب الفئة</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب الفئة</p>
                                    <a href="{{ url_for('employees.index') }}?report=category" class="btn btn-primary">
                                        <i class="fas fa-chart-pie me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-building fa-3x mb-3 text-success"></i>
                                    <h5 class="card-title">تقارير حسب الوحدة</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب الوحدة</p>
                                    <a href="{{ url_for('employees.index') }}?report=unit" class="btn btn-success">
                                        <i class="fas fa-chart-bar me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-medal fa-3x mb-3 text-info"></i>
                                    <h5 class="card-title">تقارير حسب الرتبة</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب الرتبة</p>
                                    <a href="{{ url_for('employees.index') }}?report=rank" class="btn btn-info">
                                        <i class="fas fa-chart-bar me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-clock fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">تقارير حسب الحالة</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب الحالة</p>
                                    <a href="{{ url_for('employees.index') }}?report=status" class="btn btn-warning">
                                        <i class="fas fa-chart-pie me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-check fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">تقارير حسب تاريخ التعيين</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب تاريخ التعيين</p>
                                    <a href="{{ url_for('employees.index') }}?report=hire_date" class="btn btn-primary">
                                        <i class="fas fa-chart-bar me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-award fa-3x mb-3 text-success"></i>
                                    <h5 class="card-title">تقارير حسب تاريخ آخر ترقية</h5>
                                    <p class="card-text">عرض وتصدير تقارير الموظفين حسب تاريخ آخر ترقية</p>
                                    <a href="{{ url_for('employees.index') }}?report=promotion_date" class="btn btn-success">
                                        <i class="fas fa-chart-bar me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="card h-100 border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-alt fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">تقارير حسب نوع الإجازة</h5>
                                    <p class="card-text">عرض وتصدير تقارير الإجازات حسب النوع</p>
                                    <a href="{{ url_for('leaves.index') }}?report=type" class="btn btn-danger">
                                        <i class="fas fa-chart-pie me-2"></i> عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Leave Requests -->
    <div class="row">
        <div class="col-12">
            <div class="card leaves-card">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">أحدث طلبات الإجازة</h5>
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_leaves %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in recent_leaves %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if leave.employee.profile_image %}
                                            <img src="{{ url_for('static', filename='img/employees/' + leave.employee.profile_image) }}" alt="{{ leave.employee.name }}" class="rounded-circle me-2" width="32" height="32">
                                            {% else %}
                                            <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ leave.employee.name }}" class="rounded-circle me-2" width="32" height="32">
                                            {% endif %}
                                            <span>{{ leave.employee.name }}</span>
                                        </div>
                                    </td>
                                    <td><span class="fw-medium">{{ leave.leave_type_rel.name }}</span></td>
                                    <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                    <td><span class="fw-bold">{{ leave.total_days }}</span></td>
                                    <td>
                                        {% if leave.status.name == 'PENDING' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                        {% elif leave.status.name == 'APPROVED' %}
                                        <span class="badge bg-success">موافق عليها</span>
                                        {% elif leave.status.name == 'REJECTED' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                        {% elif leave.status.name == 'CANCELLED' %}
                                        <span class="badge bg-secondary">ملغية</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('leaves.view', id=leave.id) }}" class="btn btn-sm btn-primary btn-action">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info mb-0 d-flex align-items-center">
                        <i class="fas fa-info-circle me-2 fa-lg"></i>
                        <span>لا توجد طلبات إجازة حديثة.</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('leaves.index') }}" class="btn btn-primary btn-action">
                        <i class="fas fa-list"></i> عرض جميع طلبات الإجازة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    // Common chart options
    const commonOptions = {
        maintainAspectRatio: false,
        responsive: true,
        animation: {
            duration: 1000,
            easing: 'easeOutQuart'
        },
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    font: {
                        size: 14,
                        family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                    },
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle',
                    color: '#333333'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleFont: {
                    size: 14,
                    family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                    weight: 'bold'
                },
                bodyFont: {
                    size: 13,
                    family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                },
                padding: 15,
                cornerRadius: 10,
                displayColors: true,
                boxPadding: 5,
                rtl: true,
                textDirection: 'rtl'
            },
            title: {
                display: false,
                text: '',
                font: {
                    size: 16,
                    family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                    weight: 'bold'
                },
                color: '#333333',
                padding: {
                    top: 10,
                    bottom: 20
                }
            }
        }
    };

    // Pie chart options
    const pieOptions = {
        ...commonOptions,
        cutout: '15%',
        radius: '85%',
        plugins: {
            ...commonOptions.plugins,
            legend: {
                ...commonOptions.plugins.legend,
                position: 'right'
            }
        }
    };

    // Bar chart options
    const barOptions = {
        ...commonOptions,
        plugins: {
            ...commonOptions.plugins,
            legend: {
                ...commonOptions.plugins.legend,
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0,
                    font: {
                        size: 12,
                        family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                    }
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 12,
                        family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                    }
                },
                grid: {
                    display: false
                }
            }
        }
    };

    // Category Chart
    var categoryCtx = document.getElementById('categoryChart').getContext('2d');
    var categoryChart = new Chart(categoryCtx, {
        type: 'pie',
        data: {
            labels: {{ category_labels|tojson }},
            datasets: [{
                data: {{ category_data|tojson }},
                backgroundColor: [
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(28, 200, 138, 0.8)',
                    'rgba(54, 185, 204, 0.8)',
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(231, 74, 59, 0.8)'
                ],
                hoverBackgroundColor: [
                    'rgba(78, 115, 223, 1)',
                    'rgba(28, 200, 138, 1)',
                    'rgba(54, 185, 204, 1)',
                    'rgba(246, 194, 62, 1)',
                    'rgba(231, 74, 59, 1)'
                ],
                hoverBorderColor: "#ffffff",
                borderWidth: 2,
                borderColor: '#ffffff'
            }],
        },
        options: {
            ...pieOptions,
            plugins: {
                ...pieOptions.plugins,
                tooltip: {
                    ...pieOptions.plugins.tooltip,
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;
                            let total = context.dataset.data.reduce((a, b) => a + b, 0);
                            let percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Status Chart
    var statusCtx = document.getElementById('statusChart').getContext('2d');
    var statusChart = new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: {{ status_labels|tojson }},
            datasets: [{
                data: {{ status_data|tojson }},
                backgroundColor: [
                    'rgba(28, 200, 138, 0.8)',
                    'rgba(231, 74, 59, 0.8)',
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(133, 135, 150, 0.8)',
                    'rgba(54, 185, 204, 0.8)',
                    'rgba(90, 92, 105, 0.8)'
                ],
                hoverBackgroundColor: [
                    'rgba(28, 200, 138, 1)',
                    'rgba(231, 74, 59, 1)',
                    'rgba(78, 115, 223, 1)',
                    'rgba(246, 194, 62, 1)',
                    'rgba(133, 135, 150, 1)',
                    'rgba(54, 185, 204, 1)',
                    'rgba(90, 92, 105, 1)'
                ],
                hoverBorderColor: "#ffffff",
                borderWidth: 2,
                borderColor: '#ffffff'
            }],
        },
        options: {
            ...pieOptions,
            plugins: {
                ...pieOptions.plugins,
                tooltip: {
                    ...pieOptions.plugins.tooltip,
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;
                            let total = context.dataset.data.reduce((a, b) => a + b, 0);
                            let percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Unit Chart
    var unitCtx = document.getElementById('unitChart').getContext('2d');
    var unitChart = new Chart(unitCtx, {
        type: 'bar',
        data: {
            labels: {{ unit_labels|tojson }},
            datasets: [{
                label: 'عدد الموظفين',
                data: {{ unit_data|tojson }},
                backgroundColor: 'rgba(54, 185, 204, 0.8)',
                borderColor: 'rgba(54, 185, 204, 1)',
                borderWidth: 1,
                borderRadius: 5,
                maxBarThickness: 25
            }]
        },
        options: {
            ...barOptions,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0,
                        font: {
                            size: 12,
                            family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                y: {
                    ticks: {
                        font: {
                            size: 12,
                            family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Rank Chart
    var rankCtx = document.getElementById('rankChart').getContext('2d');
    var rankChart = new Chart(rankCtx, {
        type: 'bar',
        data: {
            labels: {{ rank_labels|tojson }},
            datasets: [{
                label: 'عدد الموظفين',
                data: {{ rank_data|tojson }},
                backgroundColor: 'rgba(246, 194, 62, 0.8)',
                borderColor: 'rgba(246, 194, 62, 1)',
                borderWidth: 1,
                borderRadius: 5,
                maxBarThickness: 25
            }]
        },
        options: {
            ...barOptions,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0,
                        font: {
                            size: 12,
                            family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                y: {
                    ticks: {
                        font: {
                            size: 12,
                            family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Add auto-refresh for date and time
    function updateDateTime() {
        const now = new Date();

        // تحويل التاريخ إلى التنسيق العربي
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const arabicDate = `${year}-${month}-${day}`;

        // تحويل الوقت إلى التنسيق العربي
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        const arabicTime = `${hours}:${minutes}:${seconds}`;

        const dateElements = document.querySelectorAll('.date-time div:first-child');
        const timeElements = document.querySelectorAll('.date-time div:last-child');

        dateElements.forEach(el => {
            el.textContent = arabicDate;
        });

        timeElements.forEach(el => {
            el.textContent = arabicTime;
        });
    }

    // Update time every second
    setInterval(updateDateTime, 1000);
    updateDateTime(); // Initial update
</script>
{% endblock %}
