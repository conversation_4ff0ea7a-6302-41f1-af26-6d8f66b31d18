{% extends 'layouts/base.html' %}

{% block title %}بيانات الموظف - نظام إدارة الموارد البشرية{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-user me-2"></i>بيانات الموظف: {{ employee.name }}
        </h5>
        <div>
            <a href="{{ url_for('employees.index') }}" class="btn btn-light btn-sm ms-2">
                <i class="fas fa-list me-1"></i>قائمة الموظفين
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>تم مسح رمز QR بنجاح
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 text-center mb-4">
                {% if employee.profile_image %}
                <img src="{{ url_for('static', filename='img/' + employee.profile_image) }}" alt="{{ employee.name }}" class="img-fluid rounded-circle mb-3" style="max-width: 200px; max-height: 200px;">
                {% else %}
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 200px; height: 200px;">
                    <i class="fas fa-user-circle text-secondary" style="font-size: 120px;"></i>
                </div>
                {% endif %}
                <img src="{{ url_for('static', filename='img/police_logo.png') }}" alt="Logo" width="60" height="60" class="mt-2">
                <h4>{{ employee.name }}</h4>
                <p class="badge bg-primary">{{ employee.military_rank }}</p>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th style="width: 40%;">الرقم العسكري</th>
                                    <td>{{ employee.military_id }}</td>
                                </tr>
                                <tr>
                                    <th>العمل المكلف به</th>
                                    <td>{{ employee.position }}</td>
                                </tr>
                                <tr>
                                    <th>الوحدة</th>
                                    <td>{{ employee.department_name }}</td>
                                </tr>
                                <tr>
                                    <th>الفئة</th>
                                    <td>{{ employee.employee_category or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <th>حالة الموظف</th>
                                    <td>
                                        {% if employee.employee_status == 'مستمر' %}
                                            <span class="badge bg-success">مستمر</span>
                                        {% elif employee.employee_status == 'غياب وهروب' %}
                                            <span class="badge bg-danger">غياب وهروب</span>
                                        {% elif employee.employee_status == 'مكلف' %}
                                            <span class="badge bg-primary">مكلف</span>
                                        {% elif employee.employee_status == 'اجازة' %}
                                            <span class="badge bg-info">اجازة</span>
                                        {% elif employee.employee_status == 'ايقاف عن العمل' %}
                                            <span class="badge bg-warning text-dark">ايقاف عن العمل</span>
                                        {% elif employee.employee_status == 'متفرق' %}
                                            <span class="badge bg-secondary">متفرق</span>
                                        {% elif employee.employee_status == 'عيادة طبية' %}
                                            <span class="badge bg-info text-dark">عيادة طبية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
