import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Create sample data
data = {
    'الاسم': [
        'محمد أحمد علي',
        'أحمد محمد خالد',
        'عمر سعيد محمود',
        'خال<PERSON> عبدالله سالم',
        'سعيد محمد عبدالرحمن'
    ],
    'الرقم العسكري': [
        '10001',
        '10002',
        '10003',
        '10004',
        '10005'
    ],
    'الوحدة': [
        'الوحدة الأولى',
        'الوحدة الثانية',
        'الوحدة الأولى',
        'الوحدة الثالثة',
        'الوحدة الثانية'
    ],
    'العمل المكلف به': [
        'مدير قسم',
        'مساعد مدير',
        'موظف إداري',
        'مسؤول تقني',
        'مسؤول مالي'
    ],
    'الرتبة': [
        'ملازم',
        'نقيب',
        'رائد',
        'مقدم',
        'عقيد'
    ],
    'الفئة': [
        'ضباط',
        'ضباط',
        'ضباط',
        'ضباط',
        'ضباط'
    ],
    'الحالة': [
        'مستمر',
        'مستمر',
        'إجازة',
        'مستمر',
        'مستمر'
    ],
    'الرقم الوطني': [
        '1234567890',
        '0987654321',
        '1122334455',
        '5566778899',
        '9988776655'
    ],
    'فصيلة الدم': [
        'A+',
        'B+',
        'O+',
        'AB+',
        'A-'
    ],
    'تاريخ الميلاد': [
        '1985-05-15',
        '1980-10-20',
        '1990-03-25',
        '1982-12-10',
        '1988-07-05'
    ],
    'مكان الميلاد': [
        'طرابلس',
        'بنغازي',
        'مصراتة',
        'سبها',
        'الزاوية'
    ],
    'المؤهل العلمي': [
        'بكالوريوس',
        'ماجستير',
        'بكالوريوس',
        'دكتوراه',
        'بكالوريوس'
    ],
    'تاريخ المؤهل': [
        '2010-06-30',
        '2015-05-20',
        '2012-07-15',
        '2018-09-10',
        '2011-06-25'
    ],
    'المصرف': [
        'مصرف الجمهورية',
        'مصرف الوحدة',
        'المصرف التجاري',
        'مصرف الجمهورية',
        'مصرف الوحدة'
    ],
    'رقم الحساب': [
        '123456789',
        '987654321',
        '112233445',
        '556677889',
        '998877665'
    ],
    'رقم الهاتف': [
        '0911234567',
        '0921234567',
        '0941234567',
        '0951234567',
        '0911234568'
    ],
    'البريد الإلكتروني': [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    'السكن الحالي': [
        'طرابلس - عين زارة',
        'بنغازي - المدينة',
        'مصراتة - وسط المدينة',
        'طرابلس - تاجوراء',
        'طرابلس - جنزور'
    ]
}

# Create DataFrame
df = pd.DataFrame(data)

# Save to Excel
df.to_excel('sample_employees.xlsx', index=False)

print("تم إنشاء ملف Excel نموذجي بنجاح: sample_employees.xlsx")
